

import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Card, Button, Badge, Spinner } from "flowbite-react";
import { productAPI } from "../../../services/api";
import type { Salad } from "../../../services/api";

const Salads = () => {
  const [salads, setSalads] = useState<Salad[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSalads = async () => {
      try {
        setLoading(true);
        const data = await productAPI.getSalads();
        setSalads(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch salads');
      } finally {
        setLoading(false);
      }
    };

    fetchSalads();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Fresh & Healthy Salads</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Enjoy our selection of fresh, crisp salads made with the finest ingredients and delicious dressings.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {salads.map((salad) => (
          <Card key={salad.id} className="max-w-sm mx-auto">
            <img
              src={salad.image_url || "/api/placeholder/300/200"}
              alt={salad.name}
              className="h-48 w-full object-cover rounded-t-lg"
            />
            <div className="p-5">
              <div className="flex justify-between items-start mb-2">
                <h5 className="text-xl font-bold tracking-tight text-gray-900">
                  {salad.name}
                </h5>
                <div className="flex flex-col space-y-1">
                  <Badge color={salad.is_available ? "success" : "failure"}>
                    {salad.is_available ? "Available" : "Unavailable"}
                  </Badge>
                  {salad.is_vegetarian && (
                    <Badge color="green" size="sm">
                      Vegetarian
                    </Badge>
                  )}
                </div>
              </div>

              <p className="font-normal text-gray-700 mb-3 line-clamp-3">
                {salad.description}
              </p>

              <div className="mb-3">
                <span className="text-sm text-gray-500">
                  Dressing: <span className="font-medium">{salad.dressing}</span>
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-gray-900">
                  ${salad.price.toFixed(2)}
                </span>
                <Link to={`/salads/${salad.id}`}>
                  <Button size="sm" disabled={!salad.is_available}>
                    View Details
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {salads.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold text-gray-600 mb-2">No salads available</h3>
          <p className="text-gray-500">Check back later for our fresh salads!</p>
        </div>
      )}
    </div>
  );
};

export default Salads;
