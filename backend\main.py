from contextlib import asynccontextmanager
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

from backend.DataBase.DataBase import get_db, engine, Base
# Import all models to ensure they are registered with SQLAlchemy
import backend.Models
from backend.Routes.User.UserRoutes import User_Router
from backend.Routes.User.AddressRoutes import Address_Router
# Temporarily disabled due to MenuItem dependency
# from backend.Routes.User.CommentRoutes import  Comment_Router
# from backend.Routes.User.ReviewRoutes import Review_Router
from backend.Routes.Category.CategoryRoutes import Category_Router
from backend.Routes.Doner.DonerRoutes import Doner_Router
from backend.Routes.Drink.DrinkRoutes import Drink_Router
from backend.Routes.Ingredient.IngredientRoutes import Ingredient_Router
from backend.Routes.Kebab.KebabRoutes import Kebab_Router
from backend.Routes.Menu.MenuRoutes import Menu_Router
from backend.Routes.Message.MessageRoutes import Message_Router
from backend.Routes.Order.OrderRoutes import Order_Router
# Temporarily disabled due to MenuItem dependency
# from backend.Routes.Order.OrderItemRoutes import OrderItem_Router
from backend.Routes.Salad.SaladRoutes import Salad_Router
from backend.Routes.Cart.CartRoutes import Cart_Router





# Lifespan event handler for database initialization
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    Base.metadata.create_all(bind=engine)
    print("Database tables created (if they didn't exist).")

    from backend.DataBase.Seed.seed_categories import seed_categories
    from backend.DataBase.Seed.seed_ingredients import seed_ingredients
    from backend.DataBase.Seed.seed_doners import seed_doners
    from backend.DataBase.Seed.seed_kebabs import seed_kebabs
    from backend.DataBase.Seed.seed_salads import seed_salads
    from backend.DataBase.Seed.seed_drinks import seed_drinks
    from backend.DataBase.Seed.seed_menus import seed_menus
    from backend.DataBase.Seed.seed_admin import seed_admin

    from backend.DataBase.DataBase import SessionLocal
    db = SessionLocal()
    try:
        seed_admin(db)  # Seed admin user first
        seed_categories(db)
        seed_ingredients(db)
        seed_doners(db)
        seed_kebabs(db)
        seed_salads(db)
        seed_drinks(db)
        seed_menus(db)
        print("Database seeded with initial data.")
    except Exception as e:
        print(f"Error seeding database: {e}")
    finally:
        db.close()

    yield

    # Shutdown (cleanup if needed)
    print("Application shutting down...")

# Application metadata for Swagger UI
app = FastAPI(
    title="E-Commerce API",
    description="The main API for the E-Commerce application, handling products, users, and orders.",
    version="1.0.0",
    contact={
        "name": "API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "Apache 2.0",
        "url": "https://www.apache.org/licenses/LICENSE-20.0.html",
    },
    lifespan=lifespan
)

# CORS (Cross-Origin Resource Sharing) Middleware
origins = [
    "http://localhost",
    "http://localhost:3000",  # Common port for React frontend
    "http://localhost:5173",  # Common port for Vite frontend
    # Add your deployed frontend URL here when you go to production
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



# Include API routers
app.include_router(User_Router, prefix="/users", tags=["Users"])
app.include_router(Address_Router, prefix="/users", tags=["Users"])
# Temporarily disabled due to MenuItem dependency
# app.include_router(Comment_Router, prefix="/users", tags=["Users"])
# app.include_router(Review_Router, prefix="/users", tags=["Users"])
app.include_router(Category_Router)
app.include_router(Doner_Router)
app.include_router(Drink_Router)
app.include_router(Ingredient_Router)
app.include_router(Kebab_Router)
app.include_router(Menu_Router)

app.include_router(Message_Router)
app.include_router(Order_Router)
# Temporarily disabled due to MenuItem dependency
# app.include_router(OrderItem_Router)
app.include_router(Salad_Router)
app.include_router(Cart_Router)

# A simple root endpoint
@app.get("/", tags=["Root"])
async def read_root():
    """A simple GET endpoint to confirm the API is running."""
    return {"message": "Welcome to the E-Commerce API!"}

# Database health check endpoint
@app.get("/health", tags=["Health"])
async def health_check(db: Session = Depends(get_db)):
    """Check if the database is accessible."""
    try:
        # Simple query to test database connection
        db.execute("SELECT 1")
        return {
            "status": "healthy",
            "database": "connected",
            "message": "API and database are running properly"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e)
        }