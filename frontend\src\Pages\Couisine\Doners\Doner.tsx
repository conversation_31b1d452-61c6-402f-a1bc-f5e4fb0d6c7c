
import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from "flowbite-react";
import { FaArrowLeft } from "react-icons/fa";
import { productAPI } from "../../../services/api";
import type { Doner as DonerType } from "../../../services/api";

const Doner = () => {
  const { id } = useParams<{ id: string }>();
  const [doner, setDoner] = useState<DonerType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDoner = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await productAPI.getDoner(id);
        setDoner(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch doner');
      } finally {
        setLoading(false);
      }
    };

    fetchDoner();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error || !doner) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error || 'Doner not found'}</p>
          <Link to="/doners">
            <Button className="mt-4">Back to Doners</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <Link to="/doners" className="inline-flex items-center mb-6 text-blue-600 hover:text-blue-800">
        <FaArrowLeft size={20} color="#3b82f6" />
        Back to Doners
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Section */}
        <div className="space-y-4">
          <img
            src={doner.image_url || "/api/placeholder/600/400"}
            alt={doner.name}
            className="w-full h-96 object-cover rounded-lg shadow-lg"
          />
        </div>

        {/* Details Section */}
        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-3xl font-bold text-gray-900">{doner.name}</h1>
              <Badge color={doner.is_available ? "success" : "failure"} size="lg">
                {doner.is_available ? "Available" : "Unavailable"}
              </Badge>
            </div>

            <p className="text-xl font-bold text-green-600 mb-4">
              ${doner.price.toFixed(2)}
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Description</h3>
              <p className="text-gray-600 leading-relaxed">{doner.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800">Meat Type</h4>
                  <p className="text-gray-600">{doner.meat_type}</p>
                </div>
              </Card>

              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800">Style</h4>
                  <p className="text-gray-600">{doner.style}</p>
                </div>
              </Card>
            </div>
          </div>

          <div className="space-y-4">
            <Button
              size="lg"
              className="w-full"
              disabled={!doner.is_available}
            >
              {doner.is_available ? 'Add to Cart' : 'Currently Unavailable'}
            </Button>

            <Button
              color="gray"
              size="lg"
              className="w-full"
              disabled={!doner.is_available}
            >
              Add to Menu
            </Button>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-12">
        <Card>
          <h3 className="text-xl font-bold text-gray-800 mb-4">About This Doner</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-semibold">Category:</span> Doner
            </div>
            <div>
              <span className="font-semibold">Meat:</span> {doner.meat_type}
            </div>
            <div>
              <span className="font-semibold">Serving Style:</span> {doner.style}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Doner;