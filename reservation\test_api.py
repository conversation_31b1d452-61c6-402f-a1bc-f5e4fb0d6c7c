#!/usr/bin/env python
"""
Simple test script for the reservation API
"""
import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8001/api"

def test_api():
    """Test the reservation API endpoints"""
    
    print("🧪 Testing Reservation API")
    print("=" * 50)
    
    # Test 1: Get all tables
    print("\n1. Testing GET /tables/")
    try:
        response = requests.get(f"{BASE_URL}/tables/")
        if response.status_code == 200:
            tables = response.json()
            print(f"✅ Success: Found {len(tables)} tables")
            print(f"   First table: {tables[0] if tables else 'None'}")
        else:
            print(f"❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Check availability
    print("\n2. Testing POST /check-availability/")
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    availability_data = {
        "reservation_date": tomorrow,
        "reservation_time": "19:00:00",
        "party_size": 4,
        "duration_hours": 2
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/check-availability/",
            json=availability_data,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: Found {len(result['available_tables'])} available tables")
            print(f"   Available tables: {[t['table_number'] for t in result['available_tables']]}")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Create a reservation
    print("\n3. Testing POST /reservations/")
    reservation_data = {
        "customer_name": "Test Customer",
        "customer_email": "<EMAIL>",
        "customer_phone": "+1234567890",
        "table": 2,
        "reservation_date": tomorrow,
        "reservation_time": "18:00:00",
        "party_size": 2,
        "duration_hours": 2,
        "special_requests": "Test reservation"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/reservations/",
            json=reservation_data,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code == 201:
            reservation = response.json()
            print(f"✅ Success: Created reservation")
            print(f"   Response: {reservation}")
            reservation_id = reservation.get('id')
            if reservation_id:
                print(f"   Reservation ID: {reservation_id}")
                print(f"   Customer: {reservation['customer_name']}")
                if 'table_details' in reservation:
                    print(f"   Table: {reservation['table_details']['table_number']}")

                # Test 4: Get the created reservation
                print(f"\n4. Testing GET /reservations/{reservation_id}/")
                get_response = requests.get(f"{BASE_URL}/reservations/{reservation_id}/")
                if get_response.status_code == 200:
                    print("✅ Success: Retrieved reservation details")
                else:
                    print(f"❌ Failed to retrieve: {get_response.status_code}")

                # Test 5: Confirm the reservation
                print(f"\n5. Testing POST /reservations/{reservation_id}/confirm/")
                confirm_response = requests.post(f"{BASE_URL}/reservations/{reservation_id}/confirm/")
                if confirm_response.status_code == 200:
                    confirmed = confirm_response.json()
                    print(f"✅ Success: Reservation confirmed, status: {confirmed['status']}")
                else:
                    print(f"❌ Failed to confirm: {confirm_response.status_code}")
            else:
                print("❌ No reservation ID returned")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 6: Get reservation statistics
    print("\n6. Testing GET /stats/")
    try:
        response = requests.get(f"{BASE_URL}/stats/")
        if response.status_code == 200:
            stats = response.json()
            print("✅ Success: Retrieved statistics")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API Testing Complete!")

if __name__ == '__main__':
    test_api()
