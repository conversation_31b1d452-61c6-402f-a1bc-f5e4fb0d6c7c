from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from typing import List, Optional

from ...DataBase.DataBase import get_db
from ...Models.Message.MessageModel import Message as MessageModel
from ...Models.User.UserModel import User, UserRole
from ...Schemas.MessageSchemas.MessageSchemas import Message, MessageCreate, MessageUpdate


class MessageControllers:
    
    @staticmethod
    async def get_message(db: Session, message_id: int) -> MessageModel:
        """Get a single message by ID"""
        message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
        if not message:
            raise HTTPException(status_code=404, detail="Message not found")
        return message
    
    @staticmethod
    async def get_messages(db: Session, skip: int = 0, limit: int = 100) -> List[MessageModel]:
        """Get all messages with pagination"""
        return db.query(MessageModel).offset(skip).limit(limit).all()
    
    @staticmethod
    async def create_message(db: Session, message: MessageCreate, sender_id: int) -> MessageModel:
        """Create a new message to admin"""
        # Find the admin user
        admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
        if not admin_user:
            raise HTTPException(status_code=404, detail="Admin user not found")

        # Create message data with sender_id and admin as recipient
        message_data = message.model_dump()
        message_data['sender_id'] = sender_id
        message_data['recipient_id'] = admin_user.id

        db_message = MessageModel(**message_data)
        db.add(db_message)
        db.commit()
        db.refresh(db_message)
        return db_message
    
    @staticmethod
    async def update_message(db: Session, message_id: int, message: MessageUpdate) -> MessageModel:
        """Update an existing message"""
        db_message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
        if not db_message:
            raise HTTPException(status_code=404, detail="Message not found")
        
        update_data = message.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_message, key, value)
        
        db.commit()
        db.refresh(db_message)
        return db_message
    
    @staticmethod
    async def delete_message(db: Session, message_id: int) -> MessageModel:
        """Delete a message"""
        db_message = db.query(MessageModel).filter(MessageModel.id == message_id).first()
        if not db_message:
            raise HTTPException(status_code=404, detail="Message not found")
        
        db.delete(db_message)
        db.commit()
        return db_message
