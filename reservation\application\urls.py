from django.urls import path
from . import views

urlpatterns = [
    # Table endpoints
    path('tables/', views.TableListView.as_view(), name='table-list'),
    path('tables/<int:pk>/', views.TableDetailView.as_view(), name='table-detail'),
    
    # Reservation endpoints
    path('reservations/', views.ReservationListCreateView.as_view(), name='reservation-list-create'),
    path('reservations/<int:pk>/', views.ReservationDetailView.as_view(), name='reservation-detail'),
    
    # Utility endpoints
    path('check-availability/', views.check_availability, name='check-availability'),
    path('reservations/<int:pk>/cancel/', views.cancel_reservation, name='cancel-reservation'),
    path('reservations/<int:pk>/confirm/', views.confirm_reservation, name='confirm-reservation'),
    path('stats/', views.reservation_stats, name='reservation-stats'),
]
