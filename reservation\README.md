# Restaurant Reservation Microservice

A Django REST Framework microservice for managing restaurant table reservations.

## Features

- **Table Management**: Manage restaurant tables with capacity and location information
- **Reservation System**: Create, update, cancel, and confirm table reservations
- **Availability Checking**: Check table availability for specific dates and times
- **Business Rules**: Enforce restaurant hours, capacity limits, and time conflicts
- **Statistics**: Get reservation and table statistics
- **CORS Support**: Ready for frontend integration

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run Migrations
```bash
python manage.py migrate
```

### 3. Create Sample Tables
```bash
python create_sample_data.py
```

### 4. Start the Server
```bash
python manage.py runserver 8001
```

The API will be available at `http://localhost:8001/api/`

### 5. Test the API
```bash
python test_api.py
```

## API Endpoints

- `GET /api/tables/` - List all tables
- `GET /api/tables/{id}/` - Get table details
- `GET /api/reservations/` - List reservations (with filters)
- `POST /api/reservations/` - Create new reservation
- `GET /api/reservations/{id}/` - Get reservation details
- `PUT/PATCH /api/reservations/{id}/` - Update reservation
- `DELETE /api/reservations/{id}/` - Delete reservation
- `POST /api/check-availability/` - Check table availability
- `POST /api/reservations/{id}/cancel/` - Cancel reservation
- `POST /api/reservations/{id}/confirm/` - Confirm reservation
- `GET /api/stats/` - Get reservation statistics

## Models

### Table
- `table_number`: Unique table identifier
- `capacity`: Maximum number of guests
- `is_available`: Whether table is available for reservations
- `location`: Table location description

### Reservation
- Customer information (name, email, phone)
- Table assignment
- Date and time
- Party size and duration
- Status (pending, confirmed, cancelled, completed, no_show)
- Special requests

## Business Rules

1. **Restaurant Hours**: 9:00 AM - 11:00 PM
2. **No Past Reservations**: Cannot book in the past
3. **Capacity Limits**: Party size cannot exceed table capacity
4. **No Double Booking**: Tables cannot be reserved for overlapping times
5. **Duration Limits**: 1-8 hours, default 2 hours

## Frontend Integration

The service is configured with CORS to allow requests from `http://localhost:3000` (React frontend).

Example JavaScript integration:

```javascript
// Check availability
const response = await fetch('http://localhost:8001/api/check-availability/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        reservation_date: '2025-07-15',
        reservation_time: '19:00:00',
        party_size: 4
    })
});

// Create reservation
const reservation = await fetch('http://localhost:8001/api/reservations/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        customer_name: 'John Doe',
        customer_email: '<EMAIL>',
        customer_phone: '+1234567890',
        table: 1,
        reservation_date: '2025-07-15',
        reservation_time: '19:00:00',
        party_size: 2
    })
});
```

## Admin Interface

Access the Django admin at `http://localhost:8001/admin/` to manage tables and reservations.

Create a superuser:
```bash
python manage.py createsuperuser
```

## Development

- **Database**: SQLite (default)
- **Framework**: Django 4.2.7 + Django REST Framework 3.14.0
- **CORS**: django-cors-headers 3.14.0

## Files Structure

```
reservation/
├── application/           # Main Django app
│   ├── models.py         # Table and Reservation models
│   ├── serializers.py    # API serializers
│   ├── views.py          # API views
│   ├── urls.py           # App URL configuration
│   └── admin.py          # Admin configuration
├── reservation/          # Django project settings
│   ├── settings.py       # Project settings
│   └── urls.py           # Main URL configuration
├── create_sample_data.py # Script to create sample tables
├── test_api.py           # API testing script
├── requirements.txt      # Python dependencies
├── API_DOCUMENTATION.md  # Detailed API documentation
└── README.md            # This file
```
