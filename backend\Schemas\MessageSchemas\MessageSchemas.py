from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class MessageBase(BaseModel):
    subject: str = Field(..., max_length=255)
    content: str

class MessageCreate(MessageBase):
    pass  # recipient_id will be automatically set to admin

class MessageUpdate(BaseModel):
    is_read: Optional[bool] = None

class Message(MessageBase):
    id: int
    sender_id: int
    recipient_id: int
    created_at: datetime
    is_read: bool

    class Config:
        from_attributes = True
