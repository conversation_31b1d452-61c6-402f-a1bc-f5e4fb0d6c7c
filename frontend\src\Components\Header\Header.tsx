import {
  Avatar,
  Button,
  Dropdown,
  DropdownDiv<PERSON>,
  DropdownHeader,
  DropdownI<PERSON>,
  Navbar,
  NavbarBrand,
  NavbarCollapse,
  NavbarLink,
  NavbarToggle,
} from "flowbite-react";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import Logo from '../../assets/logo.png'
import { useUserStore, useCategoryStore } from "../../store/store";


const Header = () => {
  const { isLoggedIn, user, logout } = useUserStore();
  const { categories, fetchCategories, loading } = useCategoryStore();

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Helper function to get route path for category
  const getCategoryRoute = (categoryName: string) => {
    return `/${categoryName.toLowerCase()}s`;
  };

  return (
    <Navbar fluid rounded className="relative z-50">
      <NavbarBrand href="/">
        <img src={Logo} className="mr-3 h-6 sm:h-9" alt="Flowbite React Logo" />
      </NavbarBrand>
      <div className="flex md:order-2">
        {isLoggedIn ? (
          <Dropdown
            arrowIcon={false}
            inline
            label={
              <Avatar className="cursor-pointer" alt="User Dropdown" img="data:image/png;base64,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" rounded />
            }
            className="z-50"
          >
            <DropdownHeader>
              <span className="block text-sm">{user?.name || 'User'}</span>
              <span className="block truncate text-sm font-medium">{user?.email || '<EMAIL>'}</span>
            </DropdownHeader>
            <DropdownItem href="/profile" className="hover:text-blue-600 justify-center">PROFILE</DropdownItem>
            <DropdownItem href="/orders" className="hover:text-blue-600 justify-center">ORDERS</DropdownItem>
            <DropdownItem href="/settings" className="hover:text-blue-600 justify-center">SETTINGS</DropdownItem>
            <DropdownDivider />
            <DropdownItem onClick={logout} className="text-red-600 justify-center">LOG OUT</DropdownItem>
          </Dropdown>
        ) : (
          <>
            <Button href="/register" className="mr-2">REGISTER</Button>
            <Button href="/login" color="gray" className="mr-2">LOGIN</Button>
          </>
        )}
        <NavbarToggle />
      </div>
      <NavbarCollapse>
        <NavbarLink href="/" active>
          HOME
        </NavbarLink>
        <NavbarLink href="/about">ABOUT</NavbarLink>

        <Dropdown
          inline
          label={<span className="cursor-pointer hover:text-blue-600">CATEGORIES</span>}
          trigger="hover"
          className="z-50"
        >
          {loading ? (
            <DropdownItem className="justify-center">Loading...</DropdownItem>
          ) : (
            categories.map((category) => (
              <DropdownItem
                key={category.id}
                className="justify-center hover:text-blue-600"
              >
                <Link
                  to={getCategoryRoute(category.name)}
                  className="w-full text-center"
                >
                  {category.name.toUpperCase()}S
                </Link>
              </DropdownItem>
            ))
          )}
        </Dropdown>

        <NavbarLink href="/menus">MENUS</NavbarLink>
        <NavbarLink href="/contact">CONTACT</NavbarLink>
      </NavbarCollapse>
    </Navbar>
  );
}


export default Header;