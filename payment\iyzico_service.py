import iyzipay
import json
from flask import current_app
from models import Payment, Customer, BasketItem, Transaction, PaymentStatus
from main import db
from datetime import datetime
import uuid

class IyzicoService:
    def __init__(self):
        self.options = {
            'api_key': current_app.config['IYZICO_API_KEY'],
            'secret_key': current_app.config['IYZICO_SECURITY_KEY'],
            'base_url': current_app.config['IYZICO_BASE_URL']
        }
    
    def create_payment(self, payment_data):
        """
        Create a payment request with Iyzico
        """
        try:
            # Get customer and payment from database
            payment = Payment.query.filter_by(payment_id=payment_data['payment_id']).first()
            if not payment:
                raise Exception("Payment not found")
            
            customer = payment.customer
            basket_items = payment.basket_items
            
            # Prepare payment request
            request = {
                'locale': 'tr',
                'conversationId': payment.conversation_id,
                'price': str(payment.amount),
                'paidPrice': str(payment.amount),
                'currency': 'TRY',
                'installment': '1',
                'basketId': payment.order_id,
                'paymentChannel': 'WEB',
                'paymentGroup': 'PRODUCT',
                'callbackUrl': payment_data.get('callback_url', 'http://localhost:5001/api/payment/callback'),
                'enabledInstallments': ['2', '3', '6', '9']
            }
            
            # Add buyer information
            buyer = {
                'id': customer.customer_id,
                'name': customer.name,
                'surname': customer.surname,
                'gsmNumber': customer.phone,
                'email': customer.email,
                'identityNumber': customer.identity_number,
                'lastLoginDate': '2015-10-05 12:43:35',
                'registrationDate': '2013-04-21 15:12:09',
                'registrationAddress': customer.address,
                'ip': payment_data.get('ip', '************'),
                'city': customer.city,
                'country': customer.country,
                'zipCode': customer.zip_code
            }
            request['buyer'] = buyer
            
            # Add shipping address
            shipping_address = {
                'contactName': f"{customer.name} {customer.surname}",
                'city': customer.city,
                'country': customer.country,
                'address': customer.address,
                'zipCode': customer.zip_code
            }
            request['shippingAddress'] = shipping_address
            
            # Add billing address (same as shipping for simplicity)
            request['billingAddress'] = shipping_address
            
            # Add basket items
            basket_items_list = []
            for item in basket_items:
                basket_item = {
                    'id': item.item_id,
                    'name': item.name,
                    'category1': item.category1,
                    'category2': item.category2 or 'Food',
                    'itemType': item.item_type,
                    'price': str(item.price * item.quantity)
                }
                basket_items_list.append(basket_item)
            
            request['basketItems'] = basket_items_list
            
            # Create checkout form initialize request
            checkout_form_initialize = iyzipay.CheckoutFormInitialize()
            result = checkout_form_initialize.create(request, self.options)
            
            # Parse result
            result_dict = json.loads(result.read().decode('utf-8'))
            
            if result_dict.get('status') == 'success':
                # Update payment with Iyzico data
                payment.iyzico_payment_id = result_dict.get('paymentId')
                payment.iyzico_payment_status = result_dict.get('status')
                
                # Create transaction record
                transaction = Transaction(
                    transaction_id=str(uuid.uuid4()),
                    payment_id=payment.id,
                    amount=payment.amount,
                    transaction_type='payment_initialize',
                    iyzico_transaction_id=result_dict.get('paymentId'),
                    iyzico_status=result_dict.get('status'),
                    response_data=json.dumps(result_dict)
                )
                
                db.session.add(transaction)
                db.session.commit()
                
                return {
                    'success': True,
                    'payment_page_url': result_dict.get('paymentPageUrl'),
                    'token': result_dict.get('token'),
                    'conversation_id': result_dict.get('conversationId'),
                    'payment_id': payment.payment_id
                }
            else:
                # Handle error
                error_message = result_dict.get('errorMessage', 'Unknown error')
                
                # Create failed transaction record
                transaction = Transaction(
                    transaction_id=str(uuid.uuid4()),
                    payment_id=payment.id,
                    amount=payment.amount,
                    transaction_type='payment_initialize_failed',
                    iyzico_status='failure',
                    response_data=json.dumps(result_dict),
                    error_message=error_message
                )
                
                db.session.add(transaction)
                payment.status = PaymentStatus.FAILED
                db.session.commit()
                
                return {
                    'success': False,
                    'error': error_message,
                    'error_code': result_dict.get('errorCode'),
                    'payment_id': payment.payment_id
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'payment_id': payment_data.get('payment_id')
            }
    
    def handle_callback(self, token):
        """
        Handle payment callback from Iyzico
        """
        try:
            request = {
                'locale': 'tr',
                'token': token
            }
            
            checkout_form = iyzipay.CheckoutForm()
            result = checkout_form.retrieve(request, self.options)
            
            # Parse result
            result_dict = json.loads(result.read().decode('utf-8'))
            
            if result_dict.get('status') == 'success':
                # Find payment by conversation ID
                conversation_id = result_dict.get('conversationId')
                payment = Payment.query.filter_by(conversation_id=conversation_id).first()
                
                if payment:
                    # Update payment status
                    payment.iyzico_payment_id = result_dict.get('paymentId')
                    payment.iyzico_payment_status = result_dict.get('paymentStatus')
                    payment.paid_at = datetime.utcnow()
                    
                    if result_dict.get('paymentStatus') == 'SUCCESS':
                        payment.status = PaymentStatus.SUCCESS
                    else:
                        payment.status = PaymentStatus.FAILED
                    
                    # Create transaction record
                    transaction = Transaction(
                        transaction_id=str(uuid.uuid4()),
                        payment_id=payment.id,
                        amount=payment.amount,
                        transaction_type='payment_callback',
                        iyzico_transaction_id=result_dict.get('paymentId'),
                        iyzico_status=result_dict.get('paymentStatus'),
                        response_data=json.dumps(result_dict)
                    )
                    
                    db.session.add(transaction)
                    db.session.commit()
                    
                    return {
                        'success': True,
                        'payment_status': payment.status.value,
                        'payment_id': payment.payment_id,
                        'amount': float(payment.amount),
                        'conversation_id': conversation_id
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Payment not found',
                        'conversation_id': conversation_id
                    }
            else:
                return {
                    'success': False,
                    'error': result_dict.get('errorMessage', 'Payment failed'),
                    'error_code': result_dict.get('errorCode')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def check_payment_status(self, payment_id):
        """
        Check payment status from Iyzico
        """
        try:
            payment = Payment.query.filter_by(payment_id=payment_id).first()
            if not payment:
                return {
                    'success': False,
                    'error': 'Payment not found'
                }
            
            if not payment.iyzico_payment_id:
                return {
                    'success': True,
                    'status': payment.status.value,
                    'payment_id': payment_id
                }
            
            request = {
                'locale': 'tr',
                'conversationId': payment.conversation_id,
                'paymentId': payment.iyzico_payment_id
            }
            
            payment_detail = iyzipay.Payment()
            result = payment_detail.retrieve(request, self.options)
            
            # Parse result
            result_dict = json.loads(result.read().decode('utf-8'))
            
            if result_dict.get('status') == 'success':
                # Update payment status if needed
                iyzico_status = result_dict.get('paymentStatus')
                if iyzico_status and payment.iyzico_payment_status != iyzico_status:
                    payment.iyzico_payment_status = iyzico_status
                    
                    if iyzico_status == 'SUCCESS':
                        payment.status = PaymentStatus.SUCCESS
                        if not payment.paid_at:
                            payment.paid_at = datetime.utcnow()
                    elif iyzico_status == 'FAILURE':
                        payment.status = PaymentStatus.FAILED
                    
                    db.session.commit()
                
                return {
                    'success': True,
                    'status': payment.status.value,
                    'iyzico_status': iyzico_status,
                    'payment_id': payment_id,
                    'amount': float(payment.amount),
                    'paid_at': payment.paid_at.isoformat() if payment.paid_at else None
                }
            else:
                return {
                    'success': False,
                    'error': result_dict.get('errorMessage', 'Failed to check payment status'),
                    'payment_id': payment_id
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'payment_id': payment_id
            }
