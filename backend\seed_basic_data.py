#!/usr/bin/env python3
"""
Basic seeding script for the backend database
Run this from the backend directory
"""

import sys
import os
sys.path.append('.')

from backend.DataBase.DataBase import SessionLocal, engine, Base
from backend.Models.Category.CategoryModel import Category
from backend.Models.Ingredients.IngredientsModel import Ingredient
from backend.Models.Doners.DonerModel import Doner
from backend.Models.Kebabs.KebabModel import Kebab
from backend.Models.Salads.SaladModel import Salad
from backend.Models.Drinks.DrinksModel import Drink

def seed_categories(db):
    """Seed the 4 main categories"""
    existing_count = db.query(Category).count()
    if existing_count > 0:
        print(f"Categories already exist ({existing_count} found), skipping.")
        return
    
    categories_data = [
        {"name": "Doner"},
        {"name": "Kebab"},
        {"name": "Salad"},
        {"name": "Drink"}
    ]
    
    categories = []
    for cat_data in categories_data:
        category = Category(**cat_data)
        categories.append(category)
        db.add(category)
    
    db.commit()
    print(f"✅ Successfully seeded {len(categories)} categories")

def seed_ingredients(db):
    """Seed basic ingredients"""
    existing_count = db.query(Ingredient).count()
    if existing_count > 0:
        print(f"Ingredients already exist ({existing_count} found), skipping.")
        return
    
    ingredients_data = [
        # Meats
        "Lamb", "Beef", "Chicken", "Ground Beef", "Ground Lamb", "Chicken Breast",
        # Vegetables
        "Onion", "Tomato", "Lettuce", "Red Cabbage", "Cucumber", "Olives", "Bell Pepper", 
        "Red Onion", "Parsley", "Mint", "Cilantro", "Pickles", "Jalapeños", "Cabbage", 
        "Carrots", "Arugula", "Spinach", "Corn", "Mushrooms",
        # Sauces & Dressings
        "Yogurt Sauce", "Garlic Sauce", "Spicy Sauce", "Vinaigrette Dressing", 
        "Tahini Sauce", "Hummus", "Tzatziki Sauce", "BBQ Sauce", "Olive Oil", 
        "Balsamic Vinaigrette", "Ranch Dressing", "Caesar Dressing",
        # Spices & Herbs
        "Black Pepper", "Salt", "Cumin", "Paprika", "Oregano", "Thyme", "Rosemary", 
        "Sumac", "Garlic Powder", "Onion Powder", "Chili Flakes",
        # Breads & Grains
        "Pita Bread", "Flatbread", "Lavash Bread", "Tortilla", "Bulgur", "Quinoa", "Rice",
        # Dairy & Cheese
        "Feta Cheese", "Mozzarella", "Cheddar Cheese", "Greek Yogurt", "Sour Cream",
        # Fruits
        "Lemon", "Pomegranate Seeds"
    ]
    
    ingredients = []
    for name in ingredients_data:
        ingredient = Ingredient(name=name)
        ingredients.append(ingredient)
        db.add(ingredient)
    
    db.commit()
    print(f"✅ Successfully seeded {len(ingredients)} ingredients")

def get_category_id(db, category_name):
    """Get category ID by name"""
    category = db.query(Category).filter(Category.name == category_name).first()
    return category.id if category else None

def seed_doners(db):
    """Seed doner items"""
    existing_count = db.query(Doner).count()
    if existing_count > 0:
        print(f"Doners already exist ({existing_count} found), skipping.")
        return
    
    doner_category_id = get_category_id(db, "Doner")
    
    doners_data = [
        {
            "name": "Classic Beef Doner",
            "description": "Tender beef slices wrapped in a warm flatbread with fresh vegetables and garlic sauce.",
            "price": 12.50,
            "meat_type": "Beef",
            "style": "Wrap",
            "image_url": "/images/doners/beef_doner.jpg",
            "category_id": doner_category_id
        },
        {
            "name": "Chicken Doner Plate",
            "description": "Juicy chicken doner served on a plate with rice, salad, and a side of tzatziki sauce.",
            "price": 15.00,
            "meat_type": "Chicken",
            "style": "Plate",
            "image_url": "/images/doners/chicken_doner_plate.jpg",
            "category_id": doner_category_id
        },
        {
            "name": "Spicy Lamb Doner",
            "description": "A fiery lamb doner with jalapeños and spicy sauce for those who like it hot.",
            "price": 13.50,
            "meat_type": "Lamb",
            "style": "Wrap",
            "image_url": "/images/doners/spicy_lamb_doner.jpg",
            "category_id": doner_category_id
        }
    ]
    
    doners = []
    for doner_data in doners_data:
        doner = Doner(**doner_data)
        doners.append(doner)
        db.add(doner)
    
    db.commit()
    print(f"✅ Successfully seeded {len(doners)} doners")

def seed_kebabs(db):
    """Seed kebab items"""
    existing_count = db.query(Kebab).count()
    if existing_count > 0:
        print(f"Kebabs already exist ({existing_count} found), skipping.")
        return
    
    kebab_category_id = get_category_id(db, "Kebab")
    
    kebabs_data = [
        {
            "name": "Adana Kebab",
            "description": "Spicy ground lamb kebab grilled to perfection, served with rice and grilled vegetables.",
            "price": 16.00,
            "meat_type": "Lamb",
            "spice_level": 4,
            "image_url": "/images/kebabs/adana_kebab.jpg",
            "category_id": kebab_category_id
        },
        {
            "name": "Chicken Shish Kebab",
            "description": "Tender chicken breast cubes marinated and grilled on skewers with vegetables.",
            "price": 14.50,
            "meat_type": "Chicken",
            "spice_level": 2,
            "image_url": "/images/kebabs/chicken_shish.jpg",
            "category_id": kebab_category_id
        },
        {
            "name": "Mixed Grill Kebab",
            "description": "A combination of lamb, beef, and chicken kebabs served with rice and salad.",
            "price": 18.00,
            "meat_type": "Mixed",
            "spice_level": 3,
            "image_url": "/images/kebabs/mixed_grill.jpg",
            "category_id": kebab_category_id
        }
    ]
    
    kebabs = []
    for kebab_data in kebabs_data:
        kebab = Kebab(**kebab_data)
        kebabs.append(kebab)
        db.add(kebab)
    
    db.commit()
    print(f"✅ Successfully seeded {len(kebabs)} kebabs")

def seed_salads(db):
    """Seed salad items"""
    existing_count = db.query(Salad).count()
    if existing_count > 0:
        print(f"Salads already exist ({existing_count} found), skipping.")
        return
    
    salad_category_id = get_category_id(db, "Salad")
    
    salads_data = [
        {
            "name": "Mediterranean Salad",
            "description": "Fresh mixed greens with olives, feta cheese, tomatoes, and olive oil dressing.",
            "price": 8.50,
            "dressing": "Olive Oil",
            "is_vegetarian": True,
            "image_url": "/images/salads/mediterranean.jpg",
            "category_id": salad_category_id
        },
        {
            "name": "Caesar Salad",
            "description": "Crisp romaine lettuce with parmesan cheese, croutons, and Caesar dressing.",
            "price": 7.50,
            "dressing": "Caesar Dressing",
            "is_vegetarian": True,
            "image_url": "/images/salads/caesar.jpg",
            "category_id": salad_category_id
        },
        {
            "name": "Shepherd Salad",
            "description": "Traditional Turkish salad with tomatoes, cucumbers, onions, and parsley.",
            "price": 6.50,
            "dressing": "Vinaigrette Dressing",
            "is_vegetarian": True,
            "image_url": "/images/salads/shepherd.jpg",
            "category_id": salad_category_id
        }
    ]
    
    salads = []
    for salad_data in salads_data:
        salad = Salad(**salad_data)
        salads.append(salad)
        db.add(salad)
    
    db.commit()
    print(f"✅ Successfully seeded {len(salads)} salads")

def seed_drinks(db):
    """Seed drink items"""
    existing_count = db.query(Drink).count()
    if existing_count > 0:
        print(f"Drinks already exist ({existing_count} found), skipping.")
        return
    
    drink_category_id = get_category_id(db, "Drink")
    
    drinks_data = [
        {
            "name": "Turkish Tea",
            "description": "Traditional Turkish black tea served in a glass.",
            "price": 2.50,
            "size_ml": 200,
            "is_carbonated": False,
            "image_url": "/images/drinks/turkish_tea.jpg",
            "category_id": drink_category_id
        },
        {
            "name": "Fresh Orange Juice",
            "description": "Freshly squeezed orange juice, rich in vitamin C.",
            "price": 4.00,
            "size_ml": 300,
            "is_carbonated": False,
            "image_url": "/images/drinks/orange_juice.jpg",
            "category_id": drink_category_id
        },
        {
            "name": "Ayran",
            "description": "Traditional Turkish yogurt drink, refreshing and healthy.",
            "price": 3.00,
            "size_ml": 250,
            "is_carbonated": False,
            "image_url": "/images/drinks/ayran.jpg",
            "category_id": drink_category_id
        },
        {
            "name": "Coca Cola",
            "description": "Classic carbonated soft drink.",
            "price": 2.50,
            "size_ml": 330,
            "is_carbonated": True,
            "image_url": "/images/drinks/coca_cola.jpg",
            "category_id": drink_category_id
        },
        {
            "name": "Sparkling Water",
            "description": "Refreshing sparkling mineral water.",
            "price": 2.00,
            "size_ml": 500,
            "is_carbonated": True,
            "image_url": "/images/drinks/sparkling_water.jpg",
            "category_id": drink_category_id
        }
    ]
    
    drinks = []
    for drink_data in drinks_data:
        drink = Drink(**drink_data)
        drinks.append(drink)
        db.add(drink)
    
    db.commit()
    print(f"✅ Successfully seeded {len(drinks)} drinks")

def main():
    """Main seeding function"""
    print("=== SEEDING BACKEND DATABASE ===\n")
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # Seed all data
        seed_categories(db)
        seed_ingredients(db)
        seed_doners(db)
        seed_kebabs(db)
        seed_salads(db)
        seed_drinks(db)
        
        print("\n🎉 All seeding completed successfully!")
        print(f"✅ Categories: {db.query(Category).count()} items")
        print(f"✅ Ingredients: {db.query(Ingredient).count()} items")
        print(f"✅ Doners: {db.query(Doner).count()} items")
        print(f"✅ Kebabs: {db.query(Kebab).count()} items")
        print(f"✅ Salads: {db.query(Salad).count()} items")
        print(f"✅ Drinks: {db.query(Drink).count()} items")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Error during seeding: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    main()
