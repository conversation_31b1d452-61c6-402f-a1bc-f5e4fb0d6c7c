

import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, Button, Badge, Spinner } from "flowbite-react";
import { productAPI } from "../../../services/api";
import type { Drink as DrinkType } from "../../../services/api";

const Drinks = () => {
  const [drinks, setDrinks] = useState<DrinkType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDrinks = async () => {
      try {
        setLoading(true);
        const data = await productAPI.getDrinks();
        setDrinks(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch drinks');
      } finally {
        setLoading(false);
      }
    };

    fetchDrinks();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Refreshing Beverages</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Quench your thirst with our selection of traditional and modern beverages, perfect to complement your meal.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {drinks.map((drink) => (
          <Card key={drink.id} className="max-w-sm mx-auto">
            <img
              src={drink.image_url || "/api/placeholder/300/200"}
              alt={drink.name}
              className="h-48 w-full object-cover rounded-t-lg"
            />
            <div className="p-5">
              <div className="flex justify-between items-start mb-2">
                <h5 className="text-xl font-bold tracking-tight text-gray-900">
                  {drink.name}
                </h5>
                <div className="flex flex-col space-y-1">
                  <Badge color={drink.is_available ? "success" : "failure"}>
                    {drink.is_available ? "Available" : "Unavailable"}
                  </Badge>
                  {drink.is_carbonated && (
                    <Badge color="blue" size="sm">
                      Carbonated
                    </Badge>
                  )}
                </div>
              </div>

              <p className="font-normal text-gray-700 mb-3 line-clamp-3">
                {drink.description}
              </p>

              <div className="mb-3">
                <span className="text-sm text-gray-500">
                  Size: <span className="font-medium">{drink.size_ml}ml</span>
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-gray-900">
                  ${drink.price.toFixed(2)}
                </span>
                <Link to={`/drinks/${drink.id}`}>
                  <Button size="sm" disabled={!drink.is_available}>
                    View Details
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {drinks.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold text-gray-600 mb-2">No drinks available</h3>
          <p className="text-gray-500">Check back later for our refreshing beverages!</p>
        </div>
      )}
    </div>
  );
};

export default Drinks;
