
import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, Button, Badge, Spinner } from "flowbite-react";
import { productAPI } from "../../../services/api";
import type { Kebab as KebabType } from "../../../services/api";

const Kebabs = () => {
  const [kebabs, setKebabs] = useState<KebabType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchKebabs = async () => {
      try {
        setLoading(true);
        const data = await productAPI.getKebabs();
        setKebabs(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch kebabs');
      } finally {
        setLoading(false);
      }
    };

    fetchKebabs();
  }, []);

  const getSpiceLevelColor = (level: number) => {
    if (level <= 2) return "success";
    if (level <= 3) return "warning";
    return "failure";
  };

  const getSpiceLevelText = (level: number) => {
    if (level <= 2) return "Mild";
    if (level <= 3) return "Medium";
    return "Spicy";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Our Grilled Kebabs</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Experience the authentic taste of Turkish kebabs, grilled to perfection with traditional spices and techniques.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {kebabs.map((kebab) => (
          <Card key={kebab.id} className="max-w-sm mx-auto">
            <img
              src={kebab.image_url || "/api/placeholder/300/200"}
              alt={kebab.name}
              className="h-48 w-full object-cover rounded-t-lg"
            />
            <div className="p-5">
              <div className="flex justify-between items-start mb-2">
                <h5 className="text-xl font-bold tracking-tight text-gray-900">
                  {kebab.name}
                </h5>
                <Badge color={kebab.is_available ? "success" : "failure"}>
                  {kebab.is_available ? "Available" : "Unavailable"}
                </Badge>
              </div>

              <p className="font-normal text-gray-700 mb-3 line-clamp-3">
                {kebab.description}
              </p>

              <div className="flex justify-between items-center mb-3">
                <span className="text-sm text-gray-500">Meat: {kebab.meat_type}</span>
                <Badge color={getSpiceLevelColor(kebab.spice_level)} size="sm">
                  {getSpiceLevelText(kebab.spice_level)} ({kebab.spice_level}/5)
                </Badge>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-gray-900">
                  ${kebab.price.toFixed(2)}
                </span>
                <Link to={`/kebabs/${kebab.id}`}>
                  <Button size="sm" disabled={!kebab.is_available}>
                    View Details
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {kebabs.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold text-gray-600 mb-2">No kebabs available</h3>
          <p className="text-gray-500">Check back later for our delicious kebabs!</p>
        </div>
      )}
    </div>
  );
};

export default Kebabs;
