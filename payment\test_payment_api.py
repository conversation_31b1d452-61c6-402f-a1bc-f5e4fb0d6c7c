#!/usr/bin/env python
"""
Test script for the Payment API
"""
import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:5001"

def test_payment_api():
    """Test the payment API endpoints"""
    
    print("🧪 Testing Payment API")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n1. Testing GET /api/health")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result['status']}")
            print(f"   Service: {result['service']}")
        else:
            print(f"❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Root endpoint
    print("\n2. Testing GET /")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result['message']}")
            print(f"   Version: {result['version']}")
        else:
            print(f"❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Payment statistics (should be empty initially)
    print("\n3. Testing GET /api/payment/stats")
    try:
        response = requests.get(f"{BASE_URL}/api/payment/stats")
        if response.status_code == 200:
            stats = response.json()
            print("✅ Success: Retrieved payment statistics")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Create a payment
    print("\n4. Testing POST /api/payment/create")
    payment_data = {
        "customer": {
            "name": "John",
            "surname": "Doe",
            "email": "<EMAIL>",
            "phone": "+905350000000",
            "identity_number": "74300864791",
            "address": "Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
            "city": "Istanbul",
            "country": "Turkey",
            "zip_code": "34732"
        },
        "payment": {
            "amount": 150.75,
            "currency": "TRY",
            "order_id": f"ORDER_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "order_description": "Restaurant order - Doner Kebab + Drink",
            "payment_method": "credit_card"
        },
        "basket_items": [
            {
                "item_id": "DONER_001",
                "name": "Chicken Doner Kebab",
                "category1": "Food",
                "category2": "Main Course",
                "item_type": "PHYSICAL",
                "price": 85.50,
                "quantity": 1
            },
            {
                "item_id": "DRINK_001",
                "name": "Coca Cola",
                "category1": "Beverages",
                "category2": "Soft Drinks",
                "item_type": "PHYSICAL",
                "price": 25.25,
                "quantity": 1
            },
            {
                "item_id": "SALAD_001",
                "name": "Caesar Salad",
                "category1": "Food",
                "category2": "Salads",
                "item_type": "PHYSICAL",
                "price": 40.00,
                "quantity": 1
            }
        ],
        "callback_url": "http://localhost:5173/payment/success",
        "ip": "************"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/payment/create",
            json=payment_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 201:
            result = response.json()
            if result['success']:
                print("✅ Success: Payment created successfully")
                print(f"   Payment ID: {result['payment_id']}")
                print(f"   Payment Page URL: {result['payment_page_url']}")
                print(f"   Token: {result['token'][:20]}...")
                
                payment_id = result['payment_id']
                
                # Test 5: Get payment details
                print(f"\n5. Testing GET /api/payment/{payment_id}")
                detail_response = requests.get(f"{BASE_URL}/api/payment/{payment_id}")
                if detail_response.status_code == 200:
                    payment_details = detail_response.json()
                    print("✅ Success: Retrieved payment details")
                    print(f"   Customer: {payment_details['customer']['name']} {payment_details['customer']['surname']}")
                    print(f"   Amount: {payment_details['payment']['amount']} {payment_details['payment']['currency']}")
                    print(f"   Status: {payment_details['payment']['status']}")
                    print(f"   Basket Items: {len(payment_details['basket_items'])}")
                else:
                    print(f"❌ Failed to get payment details: {detail_response.status_code}")
                
                # Test 6: Check payment status
                print(f"\n6. Testing GET /api/payment/status/{payment_id}")
                status_response = requests.get(f"{BASE_URL}/api/payment/status/{payment_id}")
                if status_response.status_code == 200:
                    status_result = status_response.json()
                    if status_result['success']:
                        print("✅ Success: Retrieved payment status")
                        print(f"   Status: {status_result['status']}")
                        print(f"   Payment ID: {status_result['payment_id']}")
                    else:
                        print(f"❌ Status check failed: {status_result['error']}")
                else:
                    print(f"❌ Failed to check status: {status_response.status_code}")
                
            else:
                print(f"❌ Payment creation failed: {result['error']}")
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 7: List payments
    print("\n7. Testing GET /api/payments")
    try:
        response = requests.get(f"{BASE_URL}/api/payments")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: Retrieved {len(result['payments'])} payments")
            print(f"   Total: {result['total']}")
            print(f"   Pages: {result['pages']}")
        else:
            print(f"❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 8: Updated payment statistics
    print("\n8. Testing GET /api/payment/stats (after creating payment)")
    try:
        response = requests.get(f"{BASE_URL}/api/payment/stats")
        if response.status_code == 200:
            stats = response.json()
            print("✅ Success: Retrieved updated payment statistics")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ Failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Payment API Testing Complete!")
    print("\n📝 Note: To complete a payment, visit the payment_page_url")
    print("   in a browser and use Iyzico's test card numbers.")

if __name__ == '__main__':
    test_payment_api()
