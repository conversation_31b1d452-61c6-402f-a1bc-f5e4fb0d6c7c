from sqlalchemy import Column, Integer, ForeignKey, DateTime, func, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base
import enum


class CartItemType(enum.Enum):
    MENU = "menu"
    DONER = "doner"
    KEBAB = "kebab"
    SALAD = "salad"
    DRINK = "drink"


class Cart(Base):
    __tablename__ = 'carts'

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationship to user
    user = relationship("User", back_populates="cart")

    # Relationship to cart items
    items = relationship("CartItem", back_populates="cart", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Cart(id={self.id}, user_id={self.user_id})>"


class CartItem(Base):
    __tablename__ = 'cart_items'

    id = Column(Integer, primary_key=True, index=True)
    cart_id = Column(Integer, ForeignKey('carts.id'), nullable=False)
    item_type = Column(SQLAlchemyEnum(CartItemType), nullable=False)
    quantity = Column(Integer, nullable=False, default=1)

    # Foreign keys for different item types (only one should be set based on item_type)
    menu_id = Column(Integer, ForeignKey('menus.id'), nullable=True)
    doner_id = Column(Integer, ForeignKey('doners.id'), nullable=True)
    kebab_id = Column(Integer, ForeignKey('kebabs.id'), nullable=True)
    salad_id = Column(Integer, ForeignKey('salads.id'), nullable=True)
    drink_id = Column(Integer, ForeignKey('drinks.id'), nullable=True)

    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    cart = relationship("Cart", back_populates="items")
    menu = relationship("Menu", foreign_keys=[menu_id])
    doner = relationship("Doner", foreign_keys=[doner_id])
    kebab = relationship("Kebab", foreign_keys=[kebab_id])
    salad = relationship("Salad", foreign_keys=[salad_id])
    drink = relationship("Drink", foreign_keys=[drink_id])

    def __repr__(self):
        return f"<CartItem(id={self.id}, cart_id={self.cart_id}, item_type={self.item_type.value}, quantity={self.quantity})>"

    @property
    def item_price(self):
        """Get the price of the item based on its type"""
        if self.item_type == CartItemType.MENU and self.menu:
            return self.menu.price
        elif self.item_type == CartItemType.DONER and self.doner:
            return self.doner.price
        elif self.item_type == CartItemType.KEBAB and self.kebab:
            return self.kebab.price
        elif self.item_type == CartItemType.SALAD and self.salad:
            return self.salad.price
        elif self.item_type == CartItemType.DRINK and self.drink:
            return self.drink.price
        return 0.0

    @property
    def total_price(self):
        """Calculate total price for this cart item"""
        return self.item_price * self.quantity

    @property
    def item_name(self):
        """Get the name of the item based on its type"""
        if self.item_type == CartItemType.MENU and self.menu:
            return self.menu.name
        elif self.item_type == CartItemType.DONER and self.doner:
            return self.doner.name
        elif self.item_type == CartItemType.KEBAB and self.kebab:
            return self.kebab.name
        elif self.item_type == CartItemType.SALAD and self.salad:
            return self.salad.name
        elif self.item_type == CartItemType.DRINK and self.drink:
            return self.drink.name
        return "Unknown Item"
