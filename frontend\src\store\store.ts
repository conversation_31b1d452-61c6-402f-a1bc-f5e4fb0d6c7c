import { create } from "zustand";
import { type Category, categoryAPI } from "../services/api";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface UserState {
  isLoggedIn: boolean;
  user: User | null;
  login: (userData: User) => void;
  logout: () => void;
}

interface CategoryState {
  categories: Category[];
  loading: boolean;
  error: string | null;
  fetchCategories: () => Promise<void>;
}

export const useUserStore = create<UserState>((set) => ({
  isLoggedIn: false,
  user: null,
  login: (userData: User) => set({ isLoggedIn: true, user: userData }),
  logout: () => set({ isLoggedIn: false, user: null }),
}));

export const useCategoryStore = create<CategoryState>((set, get) => ({
  categories: [],
  loading: false,
  error: null,
  fetchCategories: async () => {
    // Don't fetch if already loading or if categories already exist
    if (get().loading || get().categories.length > 0) return;

    set({ loading: true, error: null });
    try {
      const categories = await categoryAPI.getCategories();
      set({ categories, loading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch categories',
        loading: false
      });
    }
  },
}));