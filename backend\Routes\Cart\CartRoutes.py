from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from ...Controllers.Cart.CartControllers import CartControllers
from ...DataBase.DataBase import get_db
from ...Schemas.CartSchemas.CartSchemas import (
    CartResponse, CartItemResponse, AddToCartRequest, UpdateCartItemRequest
)
from ...Utils.JWT import AuthHandler

Cart_Router = APIRouter(
    prefix="/cart",
    tags=["Cart"]
)

auth_handler = AuthHandler()

# Get Cart
@Cart_Router.get("/", response_model=CartResponse)
async def get_cart(current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
    """Get user's cart with all items"""
    return await CartControllers.get_cart(current_user_id, db)

# Add Item to Cart
@Cart_Router.post("/add", response_model=CartItemResponse, status_code=status.HTTP_201_CREATED)
async def add_to_cart(
    cart_request: AddToCartRequest,
    current_user_id: int = Depends(auth_handler.auth_wrapper),
    db: Session = Depends(get_db)
):
    """Add an item to the cart"""
    return await CartControllers.add_to_cart(cart_request, current_user_id, db)

# Update Cart Item
@Cart_Router.put("/items/{cart_item_id}", response_model=CartItemResponse)
async def update_cart_item(
    cart_item_id: int,
    update_request: UpdateCartItemRequest,
    current_user_id: int = Depends(auth_handler.auth_wrapper),
    db: Session = Depends(get_db)
):
    """Update quantity of a cart item"""
    return await CartControllers.update_cart_item(cart_item_id, update_request, current_user_id, db)

# Remove Cart Item
@Cart_Router.delete("/items/{cart_item_id}")
async def remove_cart_item(
    cart_item_id: int,
    current_user_id: int = Depends(auth_handler.auth_wrapper),
    db: Session = Depends(get_db)
):
    """Remove an item from the cart"""
    return await CartControllers.remove_cart_item(cart_item_id, current_user_id, db)

# Clear Cart
@Cart_Router.delete("/clear")
async def clear_cart(
    current_user_id: int = Depends(auth_handler.auth_wrapper),
    db: Session = Depends(get_db)
):
    """Clear all items from the cart"""
    return await CartControllers.clear_cart(current_user_id, db)