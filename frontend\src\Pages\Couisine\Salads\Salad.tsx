

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from "flowbite-react";
import { productAPI } from "../../../services/api";
import { FaArrowLeft } from "react-icons/fa";
import type { Salad as SaladType } from "../../../services/api";

const Salad = () => {
  const { id } = useParams<{ id: string }>();
  const [salad, setSalad] = useState<SaladType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSalad = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await productAPI.getSalad(id);
        setSalad(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch salad');
      } finally {
        setLoading(false);
      }
    };

    fetchSalad();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error || !salad) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error || 'Salad not found'}</p>
          <Link to="/salads">
            <Button className="mt-4">Back to Salads</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <Link to="/salads" className="inline-flex items-center mb-6 text-blue-600 hover:text-blue-800">
        <FaArrowLeft size={20} color="#3b82f6" />
        Back to Salads
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Section */}
        <div className="space-y-4">
          <img
            src={salad.image_url || "/api/placeholder/600/400"}
            alt={salad.name}
            className="w-full h-96 object-cover rounded-lg shadow-lg"
          />
        </div>

        {/* Details Section */}
        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-3xl font-bold text-gray-900">{salad.name}</h1>
              <div className="flex flex-col space-y-2">
                <Badge color={salad.is_available ? "success" : "failure"} size="lg">
                  {salad.is_available ? "Available" : "Unavailable"}
                </Badge>
                {salad.is_vegetarian && (
                  <Badge color="green" size="lg">
                    🌱 Vegetarian
                  </Badge>
                )}
              </div>
            </div>

            <p className="text-xl font-bold text-green-600 mb-4">
              ${salad.price.toFixed(2)}
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Description</h3>
              <p className="text-gray-600 leading-relaxed">{salad.description}</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800 mb-2">Dressing</h4>
                  <p className="text-gray-600">{salad.dressing}</p>
                </div>
              </Card>

              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800 mb-2">Dietary Info</h4>
                  <div className="flex justify-center space-x-2">
                    {salad.is_vegetarian ? (
                      <Badge color="green">Vegetarian</Badge>
                    ) : (
                      <Badge color="gray">Contains Meat</Badge>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          <div className="space-y-4">
            <Button
              size="lg"
              className="w-full"
              disabled={!salad.is_available}
            >
              {salad.is_available ? 'Add to Cart' : 'Currently Unavailable'}
            </Button>

            <Button
              color="gray"
              size="lg"
              className="w-full"
              disabled={!salad.is_available}
            >
              Add to Menu
            </Button>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-12">
        <Card>
          <h3 className="text-xl font-bold text-gray-800 mb-4">About This Salad</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-semibold">Category:</span> Salad
            </div>
            <div>
              <span className="font-semibold">Dressing:</span> {salad.dressing}
            </div>
            <div>
              <span className="font-semibold">Dietary:</span> {salad.is_vegetarian ? 'Vegetarian' : 'Contains Meat'}
            </div>
          </div>
        </Card>
      </div>

      {/* Nutritional Benefits */}
      <div className="mt-8">
        <Card>
          <h3 className="text-xl font-bold text-gray-800 mb-4">Why Choose This Salad?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Fresh, crisp ingredients</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Healthy and nutritious</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Perfect for any meal</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-green-500">✓</span>
              <span>Made to order</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Salad;
