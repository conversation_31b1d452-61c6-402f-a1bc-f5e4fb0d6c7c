# Payment Microservice API Documentation

## Overview
A Flask-based microservice for handling payments using Iyzico payment gateway. This service provides a complete payment solution for restaurant orders with support for multiple basket items, customer management, and transaction tracking.

## Base URL
```
http://localhost:5001
```

## Authentication
Currently configured for development with no authentication required. CORS is enabled for `http://localhost:5173` (React frontend).

## Endpoints

### Health Check

#### GET /api/health
Check service health status.

**Response:**
```json
{
    "status": "healthy",
    "service": "payment-microservice",
    "timestamp": "2025-07-14T22:45:00Z"
}
```

### Payment Operations

#### POST /api/payment/create
Create a new payment request and initialize Iyzico payment.

**Request Body:**
```json
{
    "customer": {
        "name": "<PERSON>",
        "surname": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "phone": "+************",
        "identity_number": "74300864791",
        "address": "Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
        "city": "Istanbul",
        "country": "Turkey",
        "zip_code": "34732"
    },
    "payment": {
        "amount": 150.75,
        "currency": "TRY",
        "order_id": "ORDER123",
        "order_description": "Restaurant order",
        "payment_method": "credit_card"
    },
    "basket_items": [
        {
            "item_id": "DONER_001",
            "name": "Chicken Doner Kebab",
            "category1": "Food",
            "category2": "Main Course",
            "item_type": "PHYSICAL",
            "price": 85.50,
            "quantity": 1
        }
    ],
    "callback_url": "http://localhost:5173/payment/success",
    "ip": "************"
}
```

**Success Response (201):**
```json
{
    "success": true,
    "payment_id": "uuid-string",
    "payment_page_url": "https://sandbox-cpp.iyzipay.com?token=...",
    "token": "iyzico-token",
    "conversation_id": "uuid-string"
}
```

#### POST /api/payment/callback
Handle payment callback from Iyzico (called automatically after payment).

**Form Data:**
- `token`: Iyzico payment token

**Response:**
```json
{
    "success": true,
    "payment_status": "success",
    "payment_id": "uuid-string",
    "amount": 150.75
}
```

#### GET /api/payment/status/{payment_id}
Check payment status.

**Response:**
```json
{
    "success": true,
    "status": "pending",
    "payment_id": "uuid-string",
    "amount": 150.75,
    "paid_at": null
}
```

#### GET /api/payment/{payment_id}
Get detailed payment information.

**Response:**
```json
{
    "payment": {
        "id": 1,
        "payment_id": "uuid-string",
        "amount": 150.75,
        "currency": "TRY",
        "status": "pending",
        "order_id": "ORDER123",
        "created_at": "2025-07-14T22:45:00Z"
    },
    "customer": {
        "name": "John",
        "surname": "Doe",
        "email": "<EMAIL>",
        "phone": "+************"
    },
    "basket_items": [
        {
            "item_id": "DONER_001",
            "name": "Chicken Doner Kebab",
            "price": 85.50,
            "quantity": 1
        }
    ],
    "transactions": []
}
```

### Payment Management

#### GET /api/payments
List payments with optional filters.

**Query Parameters:**
- `status`: Filter by payment status (pending, success, failed, cancelled, refunded)
- `customer_email`: Filter by customer email
- `order_id`: Filter by order ID
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 10)

**Response:**
```json
{
    "payments": [],
    "total": 10,
    "pages": 1,
    "current_page": 1,
    "per_page": 10
}
```

#### GET /api/payment/stats
Get payment statistics.

**Response:**
```json
{
    "total_payments": 10,
    "successful_payments": 8,
    "failed_payments": 1,
    "pending_payments": 1,
    "total_revenue": 1250.50,
    "success_rate": 80.0
}
```

## Payment Status Values

- `pending`: Payment initialized but not completed
- `success`: Payment completed successfully
- `failed`: Payment failed
- `cancelled`: Payment cancelled by user
- `refunded`: Payment refunded

## Payment Methods

- `credit_card`: Credit card payment
- `debit_card`: Debit card payment
- `bank_transfer`: Bank transfer

## Item Types

- `PHYSICAL`: Physical products (food items)
- `VIRTUAL`: Virtual products (services)

## Error Responses

All error responses follow this format:
```json
{
    "error": "Error message description"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error

## Frontend Integration Example

```javascript
// Create payment
const createPayment = async (paymentData) => {
    const response = await fetch('http://localhost:5001/api/payment/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData)
    });
    
    const result = await response.json();
    
    if (result.success) {
        // Redirect user to payment page
        window.location.href = result.payment_page_url;
    } else {
        console.error('Payment creation failed:', result.error);
    }
};

// Check payment status
const checkPaymentStatus = async (paymentId) => {
    const response = await fetch(`http://localhost:5001/api/payment/status/${paymentId}`);
    const result = await response.json();
    
    if (result.success) {
        return result.status;
    } else {
        throw new Error(result.error);
    }
};
```

## Iyzico Test Cards

For testing payments in sandbox mode, use these test card numbers:

**Successful Payment:**
- Card Number: ****************
- Expiry: 12/30
- CVC: 123

**Failed Payment:**
- Card Number: ****************
- Expiry: 12/30
- CVC: 123

## Database Schema

The service uses SQLite with the following tables:
- `customers`: Customer information
- `payments`: Payment records
- `transactions`: Transaction history
- `basket_items`: Order items

## Configuration

Key configuration values in `main.py`:
- `IYZICO_API_KEY`: Iyzico API key
- `IYZICO_SECURITY_KEY`: Iyzico security key
- `IYZICO_BASE_URL`: Iyzico API base URL
- `SQLALCHEMY_DATABASE_URI`: Database connection string
