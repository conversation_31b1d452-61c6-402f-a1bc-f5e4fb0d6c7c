#!/usr/bin/env python
"""
<PERSON>ript to create sample tables for the reservation system
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'reservation.settings')
django.setup()

from application.models import Table

def create_sample_tables():
    """Create sample tables for the restaurant"""
    
    # Check if tables already exist
    if Table.objects.exists():
        print("Tables already exist. Skipping creation.")
        return
    
    tables_data = [
        {'table_number': 1, 'capacity': 2, 'location': 'Window side'},
        {'table_number': 2, 'capacity': 2, 'location': 'Window side'},
        {'table_number': 3, 'capacity': 4, 'location': 'Center area'},
        {'table_number': 4, 'capacity': 4, 'location': 'Center area'},
        {'table_number': 5, 'capacity': 6, 'location': 'Private area'},
        {'table_number': 6, 'capacity': 6, 'location': 'Private area'},
        {'table_number': 7, 'capacity': 8, 'location': 'Large dining area'},
        {'table_number': 8, 'capacity': 8, 'location': 'Large dining area'},
        {'table_number': 9, 'capacity': 10, 'location': 'Party area'},
        {'table_number': 10, 'capacity': 12, 'location': 'Party area'},
    ]
    
    created_tables = []
    for table_data in tables_data:
        table = Table.objects.create(**table_data)
        created_tables.append(table)
        print(f"Created: {table}")
    
    print(f"\nSuccessfully created {len(created_tables)} tables!")
    return created_tables

if __name__ == '__main__':
    create_sample_tables()
