# Restaurant Reservation API Documentation

## Base URL
```
http://localhost:8001/api/
```

## Authentication
Currently, the API allows all requests without authentication (configured for development).

## Endpoints

### Tables

#### 1. Get All Tables
- **URL**: `/tables/`
- **Method**: `GET`
- **Description**: Retrieve a list of all tables
- **Response**:
```json
[
    {
        "id": 1,
        "table_number": 1,
        "capacity": 2,
        "is_available": true,
        "location": "Window side",
        "created_at": "2025-07-14T22:37:00Z",
        "updated_at": "2025-07-14T22:37:00Z"
    }
]
```

#### 2. Get Table Details
- **URL**: `/tables/{id}/`
- **Method**: `GET`
- **Description**: Retrieve details of a specific table
- **Response**: Same as above (single object)

### Reservations

#### 3. Get All Reservations / Create Reservation
- **URL**: `/reservations/`
- **Method**: `GET` | `POST`

**GET Parameters** (optional):
- `date`: Filter by date (YYYY-MM-DD)
- `status`: Filter by status (pending, confirmed, cancelled, completed, no_show)
- `email`: Filter by customer email

**POST Body**:
```json
{
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "+1234567890",
    "table": 1,
    "reservation_date": "2025-07-15",
    "reservation_time": "19:00:00",
    "party_size": 2,
    "duration_hours": 2,
    "special_requests": "Window table preferred"
}
```

**Response**:
```json
{
    "id": 1,
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "+1234567890",
    "table": 1,
    "table_details": {
        "id": 1,
        "table_number": 1,
        "capacity": 2,
        "is_available": true,
        "location": "Window side"
    },
    "reservation_date": "2025-07-15",
    "reservation_time": "19:00:00",
    "party_size": 2,
    "duration_hours": 2,
    "status": "pending",
    "special_requests": "Window table preferred",
    "created_at": "2025-07-14T22:37:00Z",
    "updated_at": "2025-07-14T22:37:00Z"
}
```

#### 4. Get/Update/Delete Specific Reservation
- **URL**: `/reservations/{id}/`
- **Method**: `GET` | `PUT` | `PATCH` | `DELETE`
- **Description**: Retrieve, update, or delete a specific reservation

### Utility Endpoints

#### 5. Check Table Availability
- **URL**: `/check-availability/`
- **Method**: `POST`
- **Description**: Check which tables are available for a specific date, time, and party size

**Request Body**:
```json
{
    "reservation_date": "2025-07-15",
    "reservation_time": "19:00:00",
    "party_size": 4,
    "duration_hours": 2
}
```

**Response**:
```json
{
    "available_tables": [
        {
            "id": 3,
            "table_number": 3,
            "capacity": 4,
            "is_available": true,
            "location": "Center area"
        }
    ],
    "requested_date": "2025-07-15",
    "requested_time": "19:00:00",
    "party_size": 4,
    "duration_hours": 2
}
```

#### 6. Cancel Reservation
- **URL**: `/reservations/{id}/cancel/`
- **Method**: `POST`
- **Description**: Cancel a specific reservation

#### 7. Confirm Reservation
- **URL**: `/reservations/{id}/confirm/`
- **Method**: `POST`
- **Description**: Confirm a pending reservation

#### 8. Get Reservation Statistics
- **URL**: `/stats/`
- **Method**: `GET`
- **Description**: Get overall reservation statistics

**Response**:
```json
{
    "total_reservations": 10,
    "today_reservations": 3,
    "pending_reservations": 2,
    "confirmed_reservations": 5,
    "cancelled_reservations": 1,
    "total_tables": 10,
    "available_tables": 8
}
```

## Business Rules

1. **Restaurant Hours**: Reservations are only accepted between 9:00 AM and 11:00 PM
2. **Advance Booking**: Cannot make reservations in the past
3. **Table Capacity**: Party size cannot exceed table capacity
4. **Time Conflicts**: Tables cannot be double-booked for overlapping time slots
5. **Reservation Duration**: Default duration is 2 hours, maximum 8 hours

## Status Codes

- `200 OK`: Successful GET, PUT, PATCH requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Invalid data or business rule violations
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server errors

## Error Response Format

```json
{
    "field_name": ["Error message"],
    "non_field_errors": ["General error message"]
}
```

## Example Frontend Integration

```javascript
// Check availability
const checkAvailability = async (date, time, partySize) => {
    const response = await fetch('http://localhost:8001/api/check-availability/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            reservation_date: date,
            reservation_time: time,
            party_size: partySize,
            duration_hours: 2
        })
    });
    return response.json();
};

// Create reservation
const createReservation = async (reservationData) => {
    const response = await fetch('http://localhost:8001/api/reservations/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(reservationData)
    });
    return response.json();
};
```
