

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from "flowbite-react";
import { FaArrowLeft } from "react-icons/fa";
import { productAPI } from "../../../services/api";
import type { Drink as DrinkType } from "../../../services/api";

const Drink = () => {
  const { id } = useParams<{ id: string }>();
  const [drink, setDrink] = useState<DrinkType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDrink = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await productAPI.getDrink(id);
        setDrink(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch drink');
      } finally {
        setLoading(false);
      }
    };

    fetchDrink();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error || !drink) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error || 'Drink not found'}</p>
          <Link to="/drinks">
            <Button className="mt-4">Back to Drinks</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <Link to="/drinks" className="inline-flex items-center mb-6 text-blue-600 hover:text-blue-800">
        <FaArrowLeft size={20} color="#3b82f6" />
        Back to Drinks
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Section */}
        <div className="space-y-4">
          <img
            src={drink.image_url || "/api/placeholder/600/400"}
            alt={drink.name}
            className="w-full h-96 object-cover rounded-lg shadow-lg"
          />
        </div>

        {/* Details Section */}
        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-3xl font-bold text-gray-900">{drink.name}</h1>
              <div className="flex flex-col space-y-2">
                <Badge color={drink.is_available ? "success" : "failure"} size="lg">
                  {drink.is_available ? "Available" : "Unavailable"}
                </Badge>
                {drink.is_carbonated && (
                  <Badge color="blue" size="lg">
                    🫧 Carbonated
                  </Badge>
                )}
              </div>
            </div>

            <p className="text-xl font-bold text-green-600 mb-4">
              ${drink.price.toFixed(2)}
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Description</h3>
              <p className="text-gray-600 leading-relaxed">{drink.description}</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800 mb-2">Size</h4>
                  <p className="text-gray-600">{drink.size_ml}ml</p>
                </div>
              </Card>

              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800 mb-2">Type</h4>
                  <div className="flex justify-center space-x-2">
                    {drink.is_carbonated ? (
                      <Badge color="blue">Carbonated</Badge>
                    ) : (
                      <Badge color="gray">Still</Badge>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          <div className="space-y-4">
            <Button
              size="lg"
              className="w-full"
              disabled={!drink.is_available}
            >
              {drink.is_available ? 'Add to Cart' : 'Currently Unavailable'}
            </Button>

            <Button
              color="gray"
              size="lg"
              className="w-full"
              disabled={!drink.is_available}
            >
              Add to Menu
            </Button>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-12">
        <Card>
          <h3 className="text-xl font-bold text-gray-800 mb-4">About This Drink</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-semibold">Category:</span> Drink
            </div>
            <div>
              <span className="font-semibold">Size:</span> {drink.size_ml}ml
            </div>
            <div>
              <span className="font-semibold">Type:</span> {drink.is_carbonated ? 'Carbonated' : 'Still'}
            </div>
          </div>
        </Card>
      </div>

      {/* Perfect Pairing */}
      <div className="mt-8">
        <Card>
          <h3 className="text-xl font-bold text-gray-800 mb-4">Perfect Pairing</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-blue-500">🥙</span>
              <span>Great with doners</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-blue-500">🍖</span>
              <span>Perfect with kebabs</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-blue-500">🥗</span>
              <span>Refreshing with salads</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-blue-500">🍽️</span>
              <span>Complements any meal</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Drink;
