from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from ...Models.Cart.CartModel import CartItemType


class CartItemBase(BaseModel):
    item_type: CartItemType
    quantity: int = Field(default=1, ge=1)

    # Only one of these should be provided based on item_type
    menu_id: Optional[int] = None
    doner_id: Optional[int] = None
    kebab_id: Optional[int] = None
    salad_id: Optional[int] = None
    drink_id: Optional[int] = None


class CartItemCreate(CartItemBase):
    pass


class CartItemUpdate(BaseModel):
    quantity: Optional[int] = Field(None, ge=1)


class CartItemResponse(CartItemBase):
    id: int
    cart_id: int
    item_name: str
    item_price: float
    total_price: float
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CartBase(BaseModel):
    pass


class CartCreate(CartBase):
    pass


class CartResponse(CartBase):
    id: int
    user_id: int
    items: List[CartItemResponse] = []
    total_items: int = 0
    total_price: float = 0.0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AddToCartRequest(BaseModel):
    item_type: CartItemType
    item_id: int
    quantity: int = Field(default=1, ge=1)


class UpdateCartItemRequest(BaseModel):
    quantity: int = Field(..., ge=1)