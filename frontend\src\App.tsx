import { BrowserRouter, Routes, Route } from "react-router-dom";
import Home from "./Pages/Home/Home";
import Header from "./Components/Header/Header";
import About from "./Pages/About/About";
import Profile from "./Pages/Profile/Profile";
import Admin from "./Pages/Admin/Admin";
import ProtectedRoute from "./Components/Routes/ProtectedRoute";
import AdminRoute from "./Components/Routes/AdminRoute";
import Orders from "./Pages/Orders/Orders";
import Order from "./Pages/Orders/Order";
import Menus from "./Pages/Menus/Menus";
import Menu from "./Pages/Menus/Menu";
import Login from "./Pages/Authentication/Login";
import Register from "./Pages/Authentication/Register";
import FooterComponent from "./Components/Footer/Footer";
import Contact from "./Pages/Contact/Contact";
import Settings from "./Pages/Settings/Settings";
import MakeReservation from "./Pages/Reservation/MakeReservation";
import MakeOrder from "./Pages/Orders/MakeOrder";
import Reservations from "./Pages/Reservation/Reservations";
import Doners from "./Pages/Couisine/Doners/Doners";
import Doner from "./Pages/Couisine/Doners/Doner";
import Drinks from "./Pages/Couisine/Drinks/Drinks";
import Drink from "./Pages/Couisine/Drinks/Drink";
import Kebabs from "./Pages/Couisine/Kebabs/Kebabs";
import Kebab from "./Pages/Couisine/Kebabs/Kebab";
import Salads from "./Pages/Couisine/Salads/Salads";
import Salad from "./Pages/Couisine/Salads/Salad";

function App() {
  return (
    <BrowserRouter>
      <Header />

      <Routes>

        {/* Open Routes */}
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/menus" element={<Menus />} />
        <Route path="/menu/:id" element={<Menu />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/doners" element={<Doners/>}/>
        <Route path="/doners/:id" element={<Doner/>}/>
        <Route path="/drinks" element={<Drinks/>}/>
        <Route path="/drinks/:id" element={<Drink/>}/>
        <Route path="/kebabs" element={<Kebabs/>}/>
        <Route path="/kebabs/:id" element={<Kebab/>}/>
        <Route path="/salads" element={<Salads/>}/>
        <Route path="/salads/:id" element={<Salad/>}/>
        {/* Open Routes */}



        {/* Authenticated Routes */}
        <Route element={<ProtectedRoute />}>
          <Route path="/profile" element={<Profile />} />
          <Route path="/orders" element={<Orders />} />
          <Route path="/order/:id" element={<Order />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/makeReservation" element={<MakeReservation />} />
          <Route path="/reservations" element={<Reservations />} />
          <Route path="/makeOrder" element={<MakeOrder />} />
        </Route>
        {/* Authenticated Routes */}



        {/* Admin Route */}
        <Route element={<AdminRoute />}>
          <Route path="/admin" element={<Admin />} />
        </Route>
        {/* Admin Route */}



      </Routes>

      <FooterComponent />
    </BrowserRouter>
  );
}

export default App;