from sqlalchemy.orm import Session
from ...Models.Category.CategoryModel import Category


def seed_categories(db: Session):
    """
    Seed the database with the 4 main categories for the restaurant:
    - Doner
    - Kebab  
    - Salad
    - Drink
    """
    
    # Check if categories already exist
    existing_categories = db.query(Category).count()
    if existing_categories > 0:
        print("Categories already exist, skipping category seeding.")
        return
    
    # Define the 4 main categories
    categories_data = [
        {
            "name": "<PERSON><PERSON>"
        },
        {
            "name": "Kebab"
        },
        {
            "name": "Salad"
        },
        {
            "name": "Drink"
        }
    ]
    
    # Create and add categories to database
    categories = []
    for category_data in categories_data:
        category = Category(**category_data)
        categories.append(category)
        db.add(category)
    
    try:
        db.commit()
        print(f"Successfully seeded {len(categories)} categories:")
        for category in categories:
            print(f"  - {category.name} (ID: {category.id})")
    except Exception as e:
        db.rollback()
        print(f"Error seeding categories: {e}")
        raise e
