from sqlalchemy.orm import Session
from ...Models.User.UserModel import User, UserRole
from ...Utils.HashPassword import HashPassword

# Hasher instance
hasher = HashPassword()

def seed_admin(db: Session):
    """Creates an admin user if one does not already exist."""
    # Check if an admin user already exists
    admin_user = db.query(User).filter(User.role == UserRole.ADMIN).first()
    if admin_user:
        print("Admin user already exists, skipping admin seeding.")
        return

    # Define admin credentials
    admin_email = "<EMAIL>"
    admin_username = "admin"
    admin_password = "admin123"

    # Create the new admin user
    new_admin = User(
        username=admin_username,
        email=admin_email,
        hashed_password=hasher.create_hash(admin_password),
        first_name="Admin",
        last_name="User",
        phone_number="******-123-4567",
        role=UserRole.ADMIN,
        is_active=True
    )

    try:
        db.add(new_admin)
        db.commit()
        db.refresh(new_admin)
        print(f"Admin user '{new_admin.username}' created successfully.")
    except Exception as e:
        db.rollback()
        print(f"Error creating admin user: {e}")
        raise
