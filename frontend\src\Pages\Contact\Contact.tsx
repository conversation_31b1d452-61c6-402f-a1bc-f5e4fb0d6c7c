
import { useState, useEffect } from 'react';
import { <PERSON>ton, Card, Label, TextInput, Textarea, Alert, Spinner } from 'flowbite-react';
import { useUserStore } from '../../store/store';
import { messageAPI } from '../../services/api';
import type { MessageData } from '../../services/api';

const Contact = () => {
  const [formData, setFormData] = useState<MessageData>({
    subject: '',
    content: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const { isLoggedIn } = useUserStore();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear messages when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isLoggedIn) {
      setError('Please login to send a message to our team.');
      return;
    }

    if (!formData.subject.trim() || !formData.content.trim()) {
      setError('Please fill in all fields.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await messageAPI.sendMessage(formData);
      setSuccess('Your message has been sent successfully! We will get back to you soon.');
      setFormData({ subject: '', content: '' });
    } catch (err: unknown) {
      let errorMessage = 'Failed to send message. Please try again.';
      if (err && typeof err === 'object' && 'response' in err) {
        const errorObj = err as { response?: { data?: { detail?: string } } };
        errorMessage = errorObj.response?.data?.detail || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 py-12 px-4">
      {/* Background Animation Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 animate-bounce delay-1000">
          <div className="text-6xl opacity-10">📧</div>
        </div>
        <div className="absolute top-40 right-20 animate-bounce delay-2000">
          <div className="text-4xl opacity-10">💬</div>
        </div>
        <div className="absolute bottom-20 left-1/4 animate-bounce delay-3000">
          <div className="text-5xl opacity-10">📞</div>
        </div>
        <div className="absolute bottom-40 right-1/3 animate-bounce delay-4000">
          <div className="text-4xl opacity-10">🏪</div>
        </div>
        <div className="absolute top-1/2 left-10 animate-pulse delay-5000">
          <div className="text-3xl opacity-10">⭐</div>
        </div>
        <div className="absolute top-1/3 right-10 animate-pulse delay-6000">
          <div className="text-3xl opacity-10">🍽️</div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto relative z-10">
        <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          {/* Header Section */}
          <div className="text-center mb-12">
            <div className="text-6xl mb-4 animate-pulse">📞</div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-green-500 to-blue-600 bg-clip-text text-transparent mb-4">
              Get In Touch With Us
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-2">
              Feel Free To Reach Us For Suggestions or Negative - Positive Feedbacks
            </p>
            <p className="text-lg text-gray-500 dark:text-gray-500">
              We value your opinion and are here to help improve your experience
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Form */}
            <Card className="shadow-2xl border-0 backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 hover:shadow-3xl transition-all duration-300">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                    Send Us a Message
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Share your thoughts, suggestions, or feedback with our team
                  </p>
                </div>

                {/* Error Alert */}
                {error && (
                  <Alert color="failure" className="animate-shake">
                    <span className="font-medium">Error!</span> {error}
                  </Alert>
                )}

                {/* Success Alert */}
                {success && (
                  <Alert color="success" className="animate-pulse">
                    <span className="font-medium">Success!</span> {success}
                  </Alert>
                )}

                {/* Subject Field */}
                <div className="space-y-2">
                  <Label htmlFor="subject" className="text-gray-700 dark:text-gray-300">Subject</Label>
                  <TextInput
                    id="subject"
                    name="subject"
                    type="text"
                    placeholder="What's this about?"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="transform transition-all duration-300 focus:scale-105"
                    icon={() => <span className="text-blue-500">📝</span>}
                  />
                </div>

                {/* Message Content Field */}
                <div className="space-y-2">
                  <Label htmlFor="content" className="text-gray-700 dark:text-gray-300">Your Message</Label>
                  <Textarea
                    id="content"
                    name="content"
                    placeholder="Tell us what's on your mind... We appreciate both positive feedback and constructive criticism!"
                    value={formData.content}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    rows={6}
                    className="transform transition-all duration-300 focus:scale-105 resize-none"
                  />
                  <p className="text-xs text-gray-500">
                    Share your dining experience, suggestions for improvement, or any feedback you have
                  </p>
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading || !formData.subject.trim() || !formData.content.trim() || !isLoggedIn}
                  className="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
                  size="lg"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Spinner size="sm" />
                      <span>Sending Message...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <span>📧</span>
                      <span>Send Message</span>
                    </div>
                  )}
                </Button>

                {!isLoggedIn && (
                  <div className="text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Please <a href="/login" className="text-blue-500 hover:underline">login</a> to send us a message
                    </p>
                  </div>
                )}
              </form>
            </Card>

            {/* Contact Information */}
            <div className="space-y-6">
              {/* Restaurant Info Card */}
              <Card className="shadow-xl border-0 backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 hover:shadow-2xl transition-all duration-300">
                <div className="text-center">
                  <div className="text-4xl mb-4">🏪</div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
                    Taste of Turkey Restaurant
                  </h3>
                  <div className="space-y-3 text-gray-600 dark:text-gray-400">
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-green-500">📍</span>
                      <span>123 Turkish Street, Food District</span>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-blue-500">📞</span>
                      <span>+1 (555) 123-4567</span>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-purple-500">📧</span>
                      <span><EMAIL></span>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Operating Hours Card */}
              <Card className="shadow-xl border-0 backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 hover:shadow-2xl transition-all duration-300">
                <div className="text-center">
                  <div className="text-4xl mb-4">🕒</div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
                    Operating Hours
                  </h3>
                  <div className="space-y-2 text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Monday - Thursday:</span>
                      <span className="font-medium">11:00 AM - 10:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Friday - Saturday:</span>
                      <span className="font-medium">11:00 AM - 11:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sunday:</span>
                      <span className="font-medium">12:00 PM - 9:00 PM</span>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Feedback Types Card */}
              <Card className="shadow-xl border-0 backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 hover:shadow-2xl transition-all duration-300">
                <div className="text-center">
                  <div className="text-4xl mb-4">💭</div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
                    We Welcome All Feedback
                  </h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-green-600">
                        <span>✨</span>
                        <span>Compliments</span>
                      </div>
                      <div className="flex items-center gap-2 text-blue-600">
                        <span>💡</span>
                        <span>Suggestions</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-orange-600">
                        <span>⚠️</span>
                        <span>Concerns</span>
                      </div>
                      <div className="flex items-center gap-2 text-purple-600">
                        <span>🍽️</span>
                        <span>Menu Ideas</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-4">
                    Your feedback helps us serve you better!
                  </p>
                </div>
              </Card>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="mt-12 text-center">
            <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-6 backdrop-blur-sm">
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                Why Your Feedback Matters
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex flex-col items-center gap-2">
                  <span className="text-3xl">🎯</span>
                  <span className="font-medium">Improve Service Quality</span>
                  <span className="text-xs">Help us enhance your dining experience</span>
                </div>
                <div className="flex flex-col items-center gap-2">
                  <span className="text-3xl">🍽️</span>
                  <span className="font-medium">Better Menu Options</span>
                  <span className="text-xs">Suggest new dishes or improvements</span>
                </div>
                <div className="flex flex-col items-center gap-2">
                  <span className="text-3xl">🤝</span>
                  <span className="font-medium">Build Community</span>
                  <span className="text-xs">Create a better experience for everyone</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;