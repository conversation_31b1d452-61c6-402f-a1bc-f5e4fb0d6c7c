# Models package initialization
# Import all models to ensure they are registered with SQLAlchemy

# Import base models first
from .Category.CategoryModel import Category
from .Ingredients.IngredientsModel import Ingredient

# Import product models
from .Doners.DonerModel import Doner
from .Kebabs.KebabModel import <PERSON><PERSON><PERSON>
from .Salads.SaladModel import Salad
from .Drinks.DrinksModel import Drink

# Import user-related models
from .User.UserModel import User, UserRole
from .User.AddressModel import Address
# Temporarily disabled due to MenuItem dependency
# from .User.ReviewModel import Review
# from .User.CommentModel import Comment

# Import order and cart models
from .Order.OrderModel import Order
# Temporarily disabled due to MenuItem dependency
# from .Order.OrderItem import OrderItem
from .Cart.CartModel import Cart

# Import menu models
from .Menu.MenuModel import Menu
# Temporarily disabled due to relationship issues
# from .Menu.MenuItemModel import MenuItem

# Import message model
from .Message.MessageModel import Message

# Export all models
__all__ = [
    'Category',
    'Ingredient',
    'Doner',
    'Kebab',
    'Salad',
    'Drink',
    'User',
    'UserRole',
    'Address',
    # 'Review',  # Temporarily disabled
    # 'Comment',  # Temporarily disabled
    'Order',
    # 'OrderItem',  # Temporarily disabled
    'Cart',
    'Menu',
    # 'MenuItem',  # Temporarily disabled
    'Message'
]
