from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone

class Table(models.Model):
    """Model representing a restaurant table"""
    table_number = models.IntegerField(unique=True, validators=[MinValueValidator(1)])
    capacity = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(20)])
    is_available = models.BooleanField(default=True)
    location = models.CharField(max_length=100, blank=True, help_text="Table location (e.g., 'Window side', 'Private area')")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['table_number']

    def __str__(self):
        return f"Table {self.table_number} (Capacity: {self.capacity})"

class Reservation(models.Model):
    """Model representing a table reservation"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
        ('no_show', 'No Show'),
    ]

    # User reference (from main backend)
    user_id = models.IntegerField(null=False, blank=False, help_text="Reference to user in main backend")

    # Customer information
    customer_name = models.CharField(max_length=100)
    customer_email = models.EmailField()
    customer_phone = models.CharField(max_length=20)

    # Reservation details
    table = models.ForeignKey(Table, on_delete=models.CASCADE, related_name='reservations')
    reservation_date = models.DateField()
    reservation_time = models.TimeField()
    party_size = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(20)])
    duration_hours = models.IntegerField(default=2, validators=[MinValueValidator(1), MaxValueValidator(8)])

    # Status and notes
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    special_requests = models.TextField(blank=True, help_text="Any special requests or notes")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-reservation_date', '-reservation_time']
        unique_together = ['table', 'reservation_date', 'reservation_time']

    def __str__(self):
        return f"Reservation for {self.customer_name} - Table {self.table.table_number} on {self.reservation_date} at {self.reservation_time}"

    @property
    def is_past_due(self):
        """Check if the reservation is past due"""
        reservation_datetime = timezone.datetime.combine(self.reservation_date, self.reservation_time)
        return timezone.now() > timezone.make_aware(reservation_datetime)

    def clean(self):
        """Validate reservation data"""
        from django.core.exceptions import ValidationError

        # Check if party size doesn't exceed table capacity
        if self.party_size > self.table.capacity:
            raise ValidationError(f"Party size ({self.party_size}) exceeds table capacity ({self.table.capacity})")

        # Check if reservation is not in the past
        reservation_datetime = timezone.datetime.combine(self.reservation_date, self.reservation_time)
        if timezone.make_aware(reservation_datetime) < timezone.now():
            raise ValidationError("Cannot make reservations in the past")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
