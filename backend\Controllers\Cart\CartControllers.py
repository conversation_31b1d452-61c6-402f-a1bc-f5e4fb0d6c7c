from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session

from ...DataBase.DataBase import get_db
from ...Models.Cart.CartModel import Cart, CartItem, CartItemType
from ...Models.Menu.MenuModel import Menu
from ...Models.Doners.DonerModel import <PERSON>r
from ...Models.Kebabs.KebabModel import Kebab
from ...Models.Salads.SaladModel import Salad
from ...Models.Drinks.DrinksModel import Drink
from ...Schemas.CartSchemas.CartSchemas import (
    CartResponse, CartItemResponse, AddToCartRequest, UpdateCartItemRequest
)
from ...Utils.JWT import AuthHandler

auth_handler = AuthHandler()


class CartControllers:

    @staticmethod
    async def get_or_create_cart(user_id: int, db: Session = Depends(get_db)):
        """Get user's cart or create one if it doesn't exist"""
        cart = db.query(Cart).filter(Cart.user_id == user_id).first()
        if not cart:
            cart = Cart(user_id=user_id)
            db.add(cart)
            db.commit()
            db.refresh(cart)
        return cart

    @staticmethod
    async def get_cart(current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
        """Get user's cart with all items"""
        cart = await CartControllers.get_or_create_cart(current_user_id, db)

        # Calculate totals
        total_items = sum(item.quantity for item in cart.items)
        total_price = sum(item.total_price for item in cart.items)

        # Create response with calculated totals
        cart_response = CartResponse(
            id=cart.id,
            user_id=cart.user_id,
            items=[
                CartItemResponse(
                    id=item.id,
                    cart_id=item.cart_id,
                    item_type=item.item_type,
                    quantity=item.quantity,
                    menu_id=item.menu_id,
                    doner_id=item.doner_id,
                    kebab_id=item.kebab_id,
                    salad_id=item.salad_id,
                    drink_id=item.drink_id,
                    item_name=item.item_name,
                    item_price=item.item_price,
                    total_price=item.total_price,
                    created_at=item.created_at,
                    updated_at=item.updated_at
                ) for item in cart.items
            ],
            total_items=total_items,
            total_price=total_price,
            created_at=cart.created_at,
            updated_at=cart.updated_at
        )

        return cart_response

    @staticmethod
    async def add_to_cart(
        cart_request: AddToCartRequest,
        current_user_id: int = Depends(auth_handler.auth_wrapper),
        db: Session = Depends(get_db)
    ):
        """Add an item to the cart"""
        # Get or create cart
        cart = await CartControllers.get_or_create_cart(current_user_id, db)

        # Validate that the item exists and is available
        item_model = None
        item_id_field = None

        if cart_request.item_type == CartItemType.MENU:
            item_model = db.query(Menu).filter(Menu.id == cart_request.item_id).first()
            item_id_field = "menu_id"
        elif cart_request.item_type == CartItemType.DONER:
            item_model = db.query(Doner).filter(Doner.id == cart_request.item_id).first()
            item_id_field = "doner_id"
        elif cart_request.item_type == CartItemType.KEBAB:
            item_model = db.query(Kebab).filter(Kebab.id == cart_request.item_id).first()
            item_id_field = "kebab_id"
        elif cart_request.item_type == CartItemType.SALAD:
            item_model = db.query(Salad).filter(Salad.id == cart_request.item_id).first()
            item_id_field = "salad_id"
        elif cart_request.item_type == CartItemType.DRINK:
            item_model = db.query(Drink).filter(Drink.id == cart_request.item_id).first()
            item_id_field = "drink_id"

        if not item_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{cart_request.item_type.value.title()} with id {cart_request.item_id} not found"
            )

        # Check if item is available
        if hasattr(item_model, 'is_available') and not item_model.is_available:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{cart_request.item_type.value.title()} is not available"
            )

        # Check if item already exists in cart
        existing_item = db.query(CartItem).filter(
            CartItem.cart_id == cart.id,
            CartItem.item_type == cart_request.item_type,
            getattr(CartItem, item_id_field) == cart_request.item_id
        ).first()

        if existing_item:
            # Update quantity
            existing_item.quantity += cart_request.quantity
            db.commit()
            db.refresh(existing_item)
            cart_item = existing_item
        else:
            # Create new cart item
            cart_item_data = {
                "cart_id": cart.id,
                "item_type": cart_request.item_type,
                "quantity": cart_request.quantity,
                item_id_field: cart_request.item_id
            }
            cart_item = CartItem(**cart_item_data)
            db.add(cart_item)
            db.commit()
            db.refresh(cart_item)

        return CartItemResponse(
            id=cart_item.id,
            cart_id=cart_item.cart_id,
            item_type=cart_item.item_type,
            quantity=cart_item.quantity,
            menu_id=cart_item.menu_id,
            doner_id=cart_item.doner_id,
            kebab_id=cart_item.kebab_id,
            salad_id=cart_item.salad_id,
            drink_id=cart_item.drink_id,
            item_name=cart_item.item_name,
            item_price=cart_item.item_price,
            total_price=cart_item.total_price,
            created_at=cart_item.created_at,
            updated_at=cart_item.updated_at
        )

    @staticmethod
    async def update_cart_item(
        cart_item_id: int,
        update_request: UpdateCartItemRequest,
        current_user_id: int = Depends(auth_handler.auth_wrapper),
        db: Session = Depends(get_db)
    ):
        """Update quantity of a cart item"""
        # Get user's cart
        cart = await CartControllers.get_or_create_cart(current_user_id, db)

        # Find the cart item
        cart_item = db.query(CartItem).filter(
            CartItem.id == cart_item_id,
            CartItem.cart_id == cart.id
        ).first()

        if not cart_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cart item not found"
            )

        # Update quantity
        cart_item.quantity = update_request.quantity
        db.commit()
        db.refresh(cart_item)

        return CartItemResponse(
            id=cart_item.id,
            cart_id=cart_item.cart_id,
            item_type=cart_item.item_type,
            quantity=cart_item.quantity,
            menu_id=cart_item.menu_id,
            doner_id=cart_item.doner_id,
            kebab_id=cart_item.kebab_id,
            salad_id=cart_item.salad_id,
            drink_id=cart_item.drink_id,
            item_name=cart_item.item_name,
            item_price=cart_item.item_price,
            total_price=cart_item.total_price,
            created_at=cart_item.created_at,
            updated_at=cart_item.updated_at
        )

    @staticmethod
    async def remove_cart_item(
        cart_item_id: int,
        current_user_id: int = Depends(auth_handler.auth_wrapper),
        db: Session = Depends(get_db)
    ):
        """Remove an item from the cart"""
        # Get user's cart
        cart = await CartControllers.get_or_create_cart(current_user_id, db)

        # Find the cart item
        cart_item = db.query(CartItem).filter(
            CartItem.id == cart_item_id,
            CartItem.cart_id == cart.id
        ).first()

        if not cart_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cart item not found"
            )

        # Remove the item
        db.delete(cart_item)
        db.commit()

        return {"message": "Item removed from cart successfully"}

    @staticmethod
    async def clear_cart(
        current_user_id: int = Depends(auth_handler.auth_wrapper),
        db: Session = Depends(get_db)
    ):
        """Clear all items from the cart"""
        # Get user's cart
        cart = await CartControllers.get_or_create_cart(current_user_id, db)

        # Remove all items
        db.query(CartItem).filter(CartItem.cart_id == cart.id).delete()
        db.commit()

        return {"message": "Cart cleared successfully"}