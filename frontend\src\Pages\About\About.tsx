import { useState, useEffect } from 'react';
import { <PERSON>, Button, Badge } from "flowbite-react";




const About = () => {

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const chefs = [
    {
      name: "<PERSON><PERSON> Yilmaz",
      role: "Head Chef",
      experience: "15 years",
      specialty: "Traditional Doner & Kebab",
      image: "https://randomuser.me/api/portraits/men/75.jpg",
      description: "Master of traditional Turkish cooking techniques with over 15 years of experience in authentic cuisine.",
      awards: ["Best Turkish Chef 2023", "Culinary Excellence Award"],
      delay: "delay-100"
    },
    {
      name: "<PERSON><PERSON>",
      role: "Sous Chef",
      experience: "10 years",
      specialty: "Fresh Salads & Appetizers",
      image: "https://randomuser.me/api/portraits/women/75.jpg",
      description: "Expert in creating fresh, healthy Mediterranean dishes with a modern twist on traditional recipes.",
      awards: ["Innovation in Cuisine 2022", "Healthy Food Champion"],
      delay: "delay-200"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Grill Master",
      experience: "12 years",
      specialty: "Grilled Meats & Kebabs",
      image: "https://randomuser.me/api/portraits/men/7.jpg",
      description: "Specialist in perfectly grilled meats and traditional Turkish barbecue techniques.",
      awards: ["Grill Master Championship", "Traditional Cooking Award"],
      delay: "delay-300"
    },
    {
      name: "Ayse Demir",
      role: "Pastry Chef",
      experience: "8 years",
      specialty: "Turkish Desserts & Beverages",
      image: "https://randomuser.me/api/portraits/women/71.jpg",
      description: "Creates authentic Turkish desserts and traditional beverages that complement our main dishes perfectly.",
      awards: ["Sweet Excellence Award", "Traditional Dessert Master"],
      delay: "delay-400"
    }
  ];

  const values = [
    {
      icon: "🌱",
      title: "Fresh Ingredients",
      description: "We source the finest, freshest ingredients daily to ensure quality in every bite."
    },
    {
      icon: "👨‍👩‍👧‍👦",
      title: "Family Tradition",
      description: "Our recipes have been passed down through generations, preserving authentic flavors."
    },
    {
      icon: "🏆",
      title: "Excellence",
      description: "We strive for perfection in every dish, maintaining the highest culinary standards."
    },
    {
      icon: "❤️",
      title: "Passion",
      description: "Every meal is prepared with love and dedication to Turkish culinary heritage."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-orange-50 dark:from-gray-900 dark:to-gray-800">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4">
        <div className="absolute inset-0 bg-gradient-to-r from-orange-400/10 to-red-500/10"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 animate-bounce delay-1000">
          <div className="text-4xl opacity-20">👨‍🍳</div>
        </div>
        <div className="absolute top-40 right-20 animate-bounce delay-2000">
          <div className="text-3xl opacity-20">🏆</div>
        </div>
        <div className="absolute bottom-20 left-1/4 animate-bounce delay-3000">
          <div className="text-4xl opacity-20">🌟</div>
        </div>

        <div className="container mx-auto text-center relative z-10">
          <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-orange-500 to-red-600 bg-clip-text text-transparent">
              ABOUT OUR RESTAURANT
            </h1>
            <p className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-8 max-w-4xl mx-auto italic">
              A journey through authentic Turkish cuisine, crafted with passion and served with pride
            </p>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20 px-4 bg-white dark:bg-gray-800">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-amber-600 dark:text-white leading-relaxed text-center">
                OUR STORY
              </h2>
              <p className="text-lg text-gray-700 dark:text-gray-400 mb-6 leading-relaxed text-center">
                Our restaurant was born from a passion for authentic Turkish cuisine. We started as a small family business with a dream of sharing the rich flavors of our homeland with the world.
              </p>
              <p className="text-lg text-gray-700 dark:text-gray-400 mb-6 leading-relaxed text-center">
                Today, we are proud to be a beloved local eatery, known for our delicious doner, kebab, salads, and drinks, all made with the freshest ingredients and a lot of love.
              </p>
              <p className="text-lg text-gray-700 dark:text-gray-400 leading-relaxed text-center">
                Every dish tells a story of tradition, quality, and the warm hospitality that Turkey is famous for.
              </p>
            </div>
            <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'} delay-300`}>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=600&h=400&fit=crop"
                  alt="Restaurant Interior"
                  className="rounded-lg shadow-2xl w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 px-4 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-amber-600 dark:text-white">
              OUR VALUES
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 italic">
              What makes us special
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div
                key={value.title}
                className={`text-center transform transition-all duration-1000 hover:scale-105 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
                style={{ animationDelay: `${(index + 1) * 200}ms` }}
              >
                <div className="text-6xl mb-4 animate-bounce">{value.icon}</div>
                <h3 className="text-2xl font-bold mb-4 text-amber-500 dark:text-white">{value.title}</h3>
                <p className="text-gray-600 dark:text-gray-400 italic text-lg">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Meet Our Chefs */}
      <section className="py-20 px-4 bg-white dark:bg-gray-800">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-amber-600 dark:text-white">
              MEET OUR CHEFS
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 italic">
              The masters behind our authentic Turkish cuisine
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {chefs.map((chef) => (
              <div
                key={chef.name}
                className={`text-center transform transition-all duration-1000 hover:scale-105 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'} ${chef.delay}`}
              >
                <Card className="h-full hover:shadow-2xl transition-all duration-300 border-0 overflow-hidden group">
                  <div className="relative overflow-hidden">
                    <img
                      src={chef.image}
                      alt={chef.name}
                      className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <Badge color="warning" className="mb-2">{chef.experience}</Badge>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-2 text-gray-800 dark:text-white">{chef.name}</h3>
                    <p className="text-orange-500 font-semibold mb-2">{chef.role}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      <strong>Specialty:</strong> {chef.specialty}
                    </p>
                    <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm leading-relaxed">
                      {chef.description}
                    </p>

                    <div className="space-y-2">
                      <h4 className="font-semibold text-gray-800 dark:text-white text-sm">Awards:</h4>
                      {chef.awards.map((award, index) => (
                        <Badge key={index} color="success" className="mr-2 mb-1">
                          🏆 {award}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Location Section */}
      <section className="py-20 px-4 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-amber-600 dark:text-white">
              VISIT US
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 italic">
              Find Us In The Heart Of The City
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
              <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-xl">
                <h3 className="text-2xl font-bold mb-6 text-amber-500 dark:text-white text-center">Restaurant Information</h3>

                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">📍</div>
                    <div>
                      <h4 className="text-amber-500 dark:text-white">Address</h4>
                      <p className="text-gray-600 dark:text-gray-400 italic">Binbaşi Reşatbey, Akçay St. No:101, 35410 Gaziemir / İzmir</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-2xl">📞</div>
                    <div>
                      <h4 className="text-amber-500 dark:text-white">Phone</h4>
                      <p className="text-gray-600 dark:text-gray-400 italic">+9 (999) 999-99 99</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-2xl">🕒</div>
                    <div>
                      <h4 className="text-amber-500 dark:text-white">Hours</h4>
                      <p className="text-gray-600 dark:text-gray-400 italic">
                        Mon-Thu: 11:00 AM - 10:00 PM<br/>
                        Fri-Sat: 11:00 AM - 11:00 PM<br/>
                        Sunday: 12:00 PM - 9:00 PM
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-2xl">✉️</div>
                    <div>
                      <h4 className="font-semibold text-amber-500 dark:text-white">Email</h4>
                      <p className="text-gray-600 dark:text-gray-400 italic"><EMAIL></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'} delay-300`}>
              <div className="relative rounded-lg overflow-hidden shadow-2xl">
                  <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3129.4452739410963!2d27.132495075910022!3d38.33867487184876!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14bbdfcf31066537%3A0x21b7fd5048c61be!2s%C4%B0zmir%20Optimum%20AVM!5e0!3m2!1str!2str!4v1752576637354!5m2!1str!2str" width="600" height="450" style={{border:0}} allowFullScreen={true} loading="lazy" referrerPolicy="no-referrer-when-downgrade">
                  </iframe>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 px-4 bg-gradient-to-r from-orange-500 to-red-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Floating Elements */}
        <div className="absolute top-10 left-10 animate-spin slow-spin">
          <div className="text-4xl opacity-30">👨‍🍳</div>
        </div>
        <div className="absolute bottom-10 right-10 animate-spin slow-spin">
          <div className="text-4xl opacity-30">🍽️</div>
        </div>
        <div className="absolute top-1/2 left-1/4 animate-pulse">
          <div className="text-3xl opacity-20">⭐</div>
        </div>

        <div className="container mx-auto text-center relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Experience Authentic Turkish Cuisine
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Book your table today and let our expert chefs take you on a culinary journey through Turkey
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="xl"
              color="light"
              className="transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              href='/makeReservation'
            >
              📅 MAKE RESERVATION
            </Button>
            <Button
              size="xl"
              color="light"
              className="transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              href='/contact'
            >
              📞 CALL US NOW
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
