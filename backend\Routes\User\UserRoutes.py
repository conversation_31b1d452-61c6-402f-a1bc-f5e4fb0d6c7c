from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.User.UserControllers import UserControllers
from ...DataBase.DataBase import get_db
from ...Schemas.UserSchemas.UserSchemas import UserCreate, UserLogin, User, UserUpdate, Token
from ...Utils.JWT import AuthHandler

User_Router = APIRouter(
    prefix="/users",
    tags=["Users"]
)

auth_handler = AuthHandler()

# Register User
@User_Router.post("/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register_user(user: UserCreate, db: Session = Depends(get_db)):
    return await UserControllers.register_user(user, db)

# Login User
@User_Router.post("/login", response_model=Token)
async def login_user(user_login: UserLogin, db: Session = Depends(get_db)):
    return await UserControllers.login_user(user_login, db)

# Get Current User Profile
@User_Router.get("/me", response_model=User)
async def get_current_user(current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
    return await UserControllers.get_single_user(current_user_id, current_user_id, db)

# Get All Users (Admin Only)
@User_Router.get("/", response_model=List[User])
async def get_all_users(current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
    return await UserControllers.get_all_users(current_user_id, db)

# Get Single User
@User_Router.get("/{user_id}", response_model=User)
async def get_single_user(user_id: int, current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
    return await UserControllers.get_single_user(user_id, current_user_id, db)

# Update User
@User_Router.put("/{user_id}", response_model=User)
async def update_user(user_id: int, user_update: UserUpdate, current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
    return await UserControllers.update_user(user_id, user_update, current_user_id, db)

# Delete User (Admin Only)
@User_Router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_id: int, current_user_id: int = Depends(auth_handler.auth_wrapper), db: Session = Depends(get_db)):
    return await UserControllers.delete_user(user_id, current_user_id, db)