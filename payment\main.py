from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///payment.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Iyzico Configuration
app.config['IYZICO_API_KEY'] = 'sandbox-********************************'
app.config['IYZICO_SECURITY_KEY'] = 'sandbox-********************************'
app.config['IYZICO_BASE_URL'] = 'sandbox-api.iyzipay.com'
app.config['IYZICO_CEP_POS'] = 'vwY6DxKBIX03FhEt'
app.config['IYZICO_CEP_POS_SECURITY_KEY'] = 'dBJcFeq4nQb1RSOzx0anbaSFNaSErZJx'

# Initialize extensions
db = SQLAlchemy(app)
CORS(app, origins=['http://localhost:5173'])  # Allow frontend access

# Import routes after app initialization
from routes import *

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, port=5001)