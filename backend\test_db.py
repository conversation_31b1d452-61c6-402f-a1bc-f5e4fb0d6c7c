#!/usr/bin/env python3
"""
Simple script to test database initialization and connection.
Run this to verify your database setup is working correctly.
"""

import sys
import os

# Add the parent directory to the path so we can import from backend
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.DataBase.DataBase import engine, Base, SessionLocal
from sqlalchemy import text

def test_database_connection():
    """Test if we can connect to the database"""
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully!")
        
        # Test connection
        db = SessionLocal()
        try:
            result = db.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            
            # Check if tables exist
            tables = db.execute(text("SELECT name FROM sqlite_master WHERE type='table'")).fetchall()
            print(f"✅ Found {len(tables)} tables in database:")
            for table in tables:
                print(f"   - {table[0]}")
                
        finally:
            db.close()
            
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_model_imports():
    """Test if we can import all models"""
    try:
        from backend.Models.User.UserModel import User
        from backend.Models.Cart.CartModel import Cart, CartItem
        from backend.Models.Doners.DonerModel import Doner
        from backend.Models.Kebabs.KebabModel import Kebab
        from backend.Models.Salads.SaladModel import Salad
        from backend.Models.Drinks.DrinksModel import Drink
        from backend.Models.Menu.MenuModel import Menu
        from backend.Models.Category.CategoryModel import Category
        from backend.Models.Ingredients.IngredientsModel import Ingredient
        
        print("✅ All models imported successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Model import error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Database Setup...")
    print("=" * 50)
    
    # Test model imports first
    models_ok = test_model_imports()
    
    # Test database connection
    db_ok = test_database_connection()
    
    print("=" * 50)
    if models_ok and db_ok:
        print("🎉 All tests passed! Your database setup is working correctly.")
        print("\n💡 You can now run your FastAPI application with:")
        print("   cd backend && python -m uvicorn main:app --reload")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)
