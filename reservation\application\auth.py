import jwt
import requests
from django.http import JsonResponse
from django.conf import settings
from functools import wraps
from rest_framework import status
from rest_framework.response import Response
from .user_utils import user_service


class AuthHandler:
    """JWT Authentication handler for reservation microservice"""
    
    def __init__(self, secret_key='SECRET', backend_url='http://localhost:8000'):
        self.secret = secret_key
        self.backend_url = backend_url
    
    def decode_token(self, token):
        """Decode JWT token and return user ID"""
        try:
            payload = jwt.decode(token, self.secret, algorithms=['HS256'])
            return payload['sub']
        except jwt.ExpiredSignatureError:
            raise Exception('Token has expired')
        except jwt.InvalidTokenError:
            raise Exception('Invalid token')
    
    def verify_user_exists(self, user_id, token):
        """Verify user exists in main backend"""
        try:
            # Make request to main backend to verify user
            response = requests.get(
                f"{self.backend_url}/users/{user_id}",
                headers={'Authorization': f'Bearer {token}'},
                timeout=5
            )
            return response.status_code == 200, response.json() if response.status_code == 200 else None
        except requests.RequestException:
            # If backend is unreachable, allow the request but log it
            return True, None


def require_auth(view_func):
    """Decorator to require authentication for Django views"""
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        auth_handler = AuthHandler()
        
        # Get token from Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            return JsonResponse({'error': 'Authorization header is required'}, status=401)
        
        if not auth_header.startswith('Bearer '):
            return JsonResponse({'error': 'Authorization header must start with Bearer'}, status=401)
        
        token = auth_header.split(' ')[1]
        
        try:
            # Decode token to get user ID
            user_id = auth_handler.decode_token(token)
            
            # Verify user exists (optional - can be disabled for performance)
            # Uncomment the following lines to enable user verification
            # if not user_service.verify_user_exists(user_id, token):
            #     return JsonResponse({'error': 'User not found'}, status=401)
            
            # Add user_id to request
            request.current_user_id = user_id
            request.current_token = token
            
            return view_func(request, *args, **kwargs)
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=401)
    
    return wrapped_view


def require_admin(view_func):
    """Decorator to require admin authentication for Django views"""
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        auth_handler = AuthHandler()
        
        # Get token from Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            return JsonResponse({'error': 'Authorization header is required'}, status=401)
        
        if not auth_header.startswith('Bearer '):
            return JsonResponse({'error': 'Authorization header must start with Bearer'}, status=401)
        
        token = auth_header.split(' ')[1]
        
        try:
            # Decode token to get user ID
            user_id = auth_handler.decode_token(token)
            
            # Verify user is admin by calling main backend
            try:
                response = requests.get(
                    f"{auth_handler.backend_url}/users/{user_id}",
                    headers={'Authorization': f'Bearer {token}'},
                    timeout=5
                )
                
                if response.status_code != 200:
                    return JsonResponse({'error': 'User not found'}, status=401)
                
                user_data = response.json()
                if user_data.get('role') != 'admin':
                    return JsonResponse({'error': 'Admin access required'}, status=403)
                    
            except requests.RequestException:
                return JsonResponse({'error': 'Could not verify admin status'}, status=503)
            
            # Add user_id to request
            request.current_user_id = user_id
            request.current_token = token
            
            return view_func(request, *args, **kwargs)
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=401)
    
    return wrapped_view


class AuthenticationMiddleware:
    """DRF Authentication class for JWT tokens"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.auth_handler = AuthHandler()

    def __call__(self, request):
        # Get token from Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            
            try:
                # Decode token to get user ID
                user_id = self.auth_handler.decode_token(token)
                request.current_user_id = user_id
                request.current_token = token
            except Exception:
                # If token is invalid, continue without authentication
                request.current_user_id = None
                request.current_token = None
        else:
            request.current_user_id = None
            request.current_token = None

        response = self.get_response(request)
        return response


def optional_auth(view_func):
    """Decorator for optional authentication (adds user_id if token is present)"""
    @wraps(view_func)
    def wrapped_view(request, *args, **kwargs):
        auth_handler = AuthHandler()
        
        # Get token from Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            
            try:
                # Decode token to get user ID
                user_id = auth_handler.decode_token(token)
                request.current_user_id = user_id
                request.current_token = token
            except Exception:
                # If token is invalid, continue without authentication
                request.current_user_id = None
                request.current_token = None
        else:
            request.current_user_id = None
            request.current_token = None
        
        return view_func(request, *args, **kwargs)
    
    return wrapped_view
