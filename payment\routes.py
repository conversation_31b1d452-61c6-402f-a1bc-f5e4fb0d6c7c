from flask import request, jsonify, redirect, url_for
from main import app, db
from models import Payment, Customer, BasketItem, Transaction, PaymentStatus, PaymentMethod
from iyzico_service import IyzicoService
from auth import require_auth, require_admin, optional_auth
import uuid
from datetime import datetime
import json

@app.route('/')
def root():
    """Root endpoint"""
    return jsonify({
        'message': 'Payment Microservice is running!',
        'status': 'healthy',
        'service': 'iyzico-payment-service',
        'version': '1.0.0'
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'payment-microservice',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/api/payment/create', methods=['POST'])
@require_auth
def create_payment():
    """
    Create a new payment request
    Expected payload:
    {
        "customer": {
            "name": "<PERSON>",
            "surname": "<PERSON><PERSON>",
            "email": "<EMAIL>",
            "phone": "+************",
            "identity_number": "74300864791",
            "address": "Nidakule Göztepe, Merdivenköy Mah. Bora Sok. No:1",
            "city": "Istanbul",
            "country": "Turkey",
            "zip_code": "34732"
        },
        "payment": {
            "amount": 100.50,
            "currency": "TRY",
            "order_id": "ORDER123",
            "order_description": "Restaurant order",
            "payment_method": "credit_card"
        },
        "basket_items": [
            {
                "item_id": "ITEM1",
                "name": "Doner Kebab",
                "category1": "Food",
                "category2": "Main Course",
                "item_type": "PHYSICAL",
                "price": 50.25,
                "quantity": 2
            }
        ],
        "callback_url": "http://localhost:5173/payment/success",
        "ip": "************"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['customer', 'payment', 'basket_items']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Create or get customer
        customer_data = data['customer']
        customer_id = str(uuid.uuid4())
        
        customer = Customer(
            customer_id=customer_id,
            name=customer_data['name'],
            surname=customer_data['surname'],
            email=customer_data['email'],
            phone=customer_data['phone'],
            identity_number=customer_data['identity_number'],
            address=customer_data['address'],
            city=customer_data['city'],
            country=customer_data['country'],
            zip_code=customer_data['zip_code']
        )
        
        db.session.add(customer)
        db.session.flush()  # Get customer ID
        
        # Create payment
        payment_data = data['payment']
        payment_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        payment = Payment(
            payment_id=payment_id,
            conversation_id=conversation_id,
            customer_id=customer.id,
            amount=payment_data['amount'],
            currency=payment_data.get('currency', 'TRY'),
            payment_method=PaymentMethod(payment_data.get('payment_method', 'credit_card')),
            order_id=payment_data['order_id'],
            order_description=payment_data.get('order_description', ''),
            status=PaymentStatus.PENDING,
            user_id=getattr(request, 'current_user_id', None)  # Associate with authenticated user
        )
        
        db.session.add(payment)
        db.session.flush()  # Get payment ID
        
        # Create basket items
        for item_data in data['basket_items']:
            basket_item = BasketItem(
                payment_id=payment.id,
                item_id=item_data['item_id'],
                name=item_data['name'],
                category1=item_data['category1'],
                category2=item_data.get('category2'),
                item_type=item_data['item_type'],
                price=item_data['price'],
                quantity=item_data.get('quantity', 1)
            )
            db.session.add(basket_item)
        
        db.session.commit()
        
        # Initialize Iyzico payment
        iyzico_service = IyzicoService()
        iyzico_data = {
            'payment_id': payment_id,
            'callback_url': data.get('callback_url', 'http://localhost:5001/api/payment/callback'),
            'ip': data.get('ip', '************')
        }
        
        result = iyzico_service.create_payment(iyzico_data)
        
        if result['success']:
            return jsonify({
                'success': True,
                'payment_id': payment_id,
                'payment_page_url': result['payment_page_url'],
                'token': result['token'],
                'conversation_id': conversation_id
            }), 201
        else:
            return jsonify({
                'success': False,
                'error': result['error'],
                'payment_id': payment_id
            }), 400
            
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/payment/callback', methods=['POST'])
def payment_callback():
    """Handle payment callback from Iyzico"""
    try:
        token = request.form.get('token')
        if not token:
            return jsonify({'error': 'Token not provided'}), 400
        
        iyzico_service = IyzicoService()
        result = iyzico_service.handle_callback(token)
        
        if result['success']:
            # Redirect to success page or return success response
            return jsonify({
                'success': True,
                'payment_status': result['payment_status'],
                'payment_id': result['payment_id'],
                'amount': result['amount']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/payment/status/<payment_id>', methods=['GET'])
@require_auth
def check_payment_status(payment_id):
    """Check payment status"""
    try:
        iyzico_service = IyzicoService()
        result = iyzico_service.check_payment_status(payment_id)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/payment/<payment_id>', methods=['GET'])
@require_auth
def get_payment_details(payment_id):
    """Get payment details"""
    try:
        payment = Payment.query.filter_by(payment_id=payment_id).first()
        if not payment:
            return jsonify({'error': 'Payment not found'}), 404

        # Check if user owns this payment
        current_user_id = getattr(request, 'current_user_id', None)
        if payment.user_id != current_user_id:
            return jsonify({'error': 'Access denied'}), 403

        # Get related data
        customer = payment.customer
        basket_items = payment.basket_items
        transactions = payment.transactions

        return jsonify({
            'payment': payment.to_dict(),
            'customer': customer.to_dict(),
            'basket_items': [item.to_dict() for item in basket_items],
            'transactions': [transaction.to_dict() for transaction in transactions]
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/payments/my', methods=['GET'])
@require_auth
def list_my_payments():
    """List current user's payments"""
    try:
        current_user_id = getattr(request, 'current_user_id', None)

        # Get query parameters
        status = request.args.get('status')
        order_id = request.args.get('order_id')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))

        # Build query for user's payments
        query = Payment.query.filter_by(user_id=current_user_id)

        if status:
            query = query.filter(Payment.status == PaymentStatus(status))

        if order_id:
            query = query.filter(Payment.order_id.contains(order_id))

        # Paginate results
        payments = query.order_by(Payment.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'payments': [payment.to_dict() for payment in payments.items],
            'total': payments.total,
            'pages': payments.pages,
            'current_page': page,
            'per_page': per_page
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/payments', methods=['GET'])
@require_admin
def list_payments():
    """List all payments with optional filters"""
    try:
        # Get query parameters
        status = request.args.get('status')
        customer_email = request.args.get('customer_email')
        order_id = request.args.get('order_id')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        # Build query
        query = Payment.query
        
        if status:
            query = query.filter(Payment.status == PaymentStatus(status))
        
        if order_id:
            query = query.filter(Payment.order_id.contains(order_id))
        
        if customer_email:
            query = query.join(Customer).filter(Customer.email.contains(customer_email))
        
        # Paginate results
        payments = query.order_by(Payment.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'payments': [payment.to_dict() for payment in payments.items],
            'total': payments.total,
            'pages': payments.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/payment/stats', methods=['GET'])
@require_admin
def payment_stats():
    """Get payment statistics"""
    try:
        total_payments = Payment.query.count()
        successful_payments = Payment.query.filter_by(status=PaymentStatus.SUCCESS).count()
        failed_payments = Payment.query.filter_by(status=PaymentStatus.FAILED).count()
        pending_payments = Payment.query.filter_by(status=PaymentStatus.PENDING).count()
        
        # Calculate total revenue
        total_revenue = db.session.query(db.func.sum(Payment.amount)).filter_by(status=PaymentStatus.SUCCESS).scalar() or 0
        
        return jsonify({
            'total_payments': total_payments,
            'successful_payments': successful_payments,
            'failed_payments': failed_payments,
            'pending_payments': pending_payments,
            'total_revenue': float(total_revenue),
            'success_rate': (successful_payments / total_payments * 100) if total_payments > 0 else 0
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return jsonify({'error': 'Internal server error'}), 500
