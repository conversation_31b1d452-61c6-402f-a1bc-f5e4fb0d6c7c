from django.contrib import admin
from .models import Table, Reservation

@admin.register(Table)
class TableAdmin(admin.ModelAdmin):
    list_display = ['table_number', 'capacity', 'is_available', 'location', 'created_at']
    list_filter = ['is_available', 'capacity']
    search_fields = ['table_number', 'location']
    ordering = ['table_number']

@admin.register(Reservation)
class ReservationAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'customer_name', 'customer_email', 'table',
        'reservation_date', 'reservation_time', 'party_size', 'status'
    ]
    list_filter = ['status', 'reservation_date', 'table']
    search_fields = ['customer_name', 'customer_email', 'customer_phone']
    ordering = ['-reservation_date', '-reservation_time']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Customer Information', {
            'fields': ('customer_name', 'customer_email', 'customer_phone')
        }),
        ('Reservation Details', {
            'fields': ('table', 'reservation_date', 'reservation_time', 'party_size', 'duration_hours')
        }),
        ('Status & Notes', {
            'fields': ('status', 'special_requests')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
