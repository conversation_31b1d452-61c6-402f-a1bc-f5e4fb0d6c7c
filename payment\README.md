# Payment Microservice

A Flask-based microservice for handling restaurant payments using Iyzico payment gateway.

## Features

- **Iyzico Integration**: Complete payment processing with Turkey's leading payment provider
- **Customer Management**: Store and manage customer information
- **Order Tracking**: Track payments with detailed transaction history
- **Basket Support**: Handle multiple items per order
- **Status Management**: Real-time payment status tracking
- **Statistics**: Payment analytics and reporting
- **CORS Support**: Ready for frontend integration

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Iyzico Keys
Update the keys in `main.py` with your Iyzico credentials:
```python
app.config['IYZICO_API_KEY'] = 'your-api-key'
app.config['IYZICO_SECURITY_KEY'] = 'your-security-key'
```

### 3. Start the Service
```bash
python main.py
```

The service will run on `http://localhost:5001`

### 4. Test the API
```bash
python test_payment_api.py
```

## API Endpoints

### Core Payment Operations
- `POST /api/payment/create` - Create new payment
- `POST /api/payment/callback` - Handle Iyzico callback
- `GET /api/payment/status/{id}` - Check payment status
- `GET /api/payment/{id}` - Get payment details

### Management
- `GET /api/payments` - List payments (with filters)
- `GET /api/payment/stats` - Payment statistics
- `GET /api/health` - Health check

## Payment Flow

1. **Create Payment**: Frontend calls `/api/payment/create` with order details
2. **Redirect**: User is redirected to Iyzico payment page
3. **Payment**: User completes payment on Iyzico
4. **Callback**: Iyzico calls `/api/payment/callback` with result
5. **Status Check**: Frontend can check status via `/api/payment/status/{id}`

## Example Usage

### Create a Payment

```javascript
const paymentData = {
    customer: {
        name: "John",
        surname: "Doe",
        email: "<EMAIL>",
        phone: "+905350000000",
        identity_number: "74300864791",
        address: "Sample Address",
        city: "Istanbul",
        country: "Turkey",
        zip_code: "34732"
    },
    payment: {
        amount: 150.75,
        currency: "TRY",
        order_id: "ORDER123",
        order_description: "Restaurant order",
        payment_method: "credit_card"
    },
    basket_items: [
        {
            item_id: "DONER_001",
            name: "Chicken Doner Kebab",
            category1: "Food",
            category2: "Main Course",
            item_type: "PHYSICAL",
            price: 85.50,
            quantity: 1
        },
        {
            item_id: "DRINK_001",
            name: "Coca Cola",
            category1: "Beverages",
            item_type: "PHYSICAL",
            price: 25.25,
            quantity: 1
        }
    ],
    callback_url: "http://localhost:5173/payment/success",
    ip: "************"
};

const response = await fetch('http://localhost:5001/api/payment/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(paymentData)
});

const result = await response.json();
if (result.success) {
    // Redirect to payment page
    window.location.href = result.payment_page_url;
}
```

### Check Payment Status

```javascript
const checkStatus = async (paymentId) => {
    const response = await fetch(`http://localhost:5001/api/payment/status/${paymentId}`);
    const result = await response.json();
    return result.status; // 'pending', 'success', 'failed', etc.
};
```

## Database Models

### Customer
- Personal information (name, email, phone)
- Address details
- Identity number (required by Iyzico)

### Payment
- Payment amount and currency
- Order information
- Status tracking
- Iyzico payment ID

### BasketItem
- Item details (name, price, quantity)
- Categories for Iyzico compliance
- Item type (PHYSICAL/VIRTUAL)

### Transaction
- Transaction history
- Iyzico responses
- Error tracking

## Configuration

### Environment Variables
You can use environment variables instead of hardcoding keys:

```python
import os
app.config['IYZICO_API_KEY'] = os.getenv('IYZICO_API_KEY', 'default-key')
app.config['IYZICO_SECURITY_KEY'] = os.getenv('IYZICO_SECURITY_KEY', 'default-key')
```

### Database
Default: SQLite (`payment.db`)
For production, update the database URI:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://user:pass@localhost/payment'
```

## Testing

### Test Cards (Sandbox)
**Successful Payment:**
- Card: ****************
- Expiry: 12/30
- CVC: 123

**Failed Payment:**
- Card: ****************
- Expiry: 12/30
- CVC: 123

### Running Tests
```bash
python test_payment_api.py
```

## Production Deployment

### 1. Use Production Keys
Replace sandbox keys with production Iyzico keys.

### 2. Use Production Database
Configure PostgreSQL or MySQL for production.

### 3. Use WSGI Server
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5001 main:app
```

### 4. Environment Variables
```bash
export IYZICO_API_KEY="prod-api-key"
export IYZICO_SECURITY_KEY="prod-security-key"
export DATABASE_URL="postgresql://..."
```

## File Structure

```
payment/
├── main.py              # Flask application setup
├── models.py            # Database models
├── routes.py            # API endpoints
├── iyzico_service.py    # Iyzico integration
├── requirements.txt     # Dependencies
├── test_payment_api.py  # API tests
├── API_DOCUMENTATION.md # Detailed API docs
└── README.md           # This file
```

## Dependencies

- **Flask**: Web framework
- **Flask-SQLAlchemy**: Database ORM
- **Flask-CORS**: Cross-origin support
- **iyzipay**: Iyzico Python SDK
- **requests**: HTTP client

## Support

For Iyzico-specific issues, refer to:
- [Iyzico Documentation](https://dev.iyzipay.com/)
- [Iyzico Python SDK](https://github.com/iyzico/iyzipay-python)

## License

This project is for educational/development purposes.
