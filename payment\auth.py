import jwt
import requests
from functools import wraps
from flask import request, jsonify, current_app
from datetime import datetime
from .user_utils import user_service


class AuthHandler:
    """JWT Authentication handler for payment microservice"""
    
    def __init__(self, secret_key='SECRET', backend_url='http://localhost:8000'):
        self.secret = secret_key
        self.backend_url = backend_url
    
    def decode_token(self, token):
        """Decode JWT token and return user ID"""
        try:
            payload = jwt.decode(token, self.secret, algorithms=['HS256'])
            return payload['sub']
        except jwt.ExpiredSignatureError:
            raise Exception('Token has expired')
        except jwt.InvalidTokenError:
            raise Exception('Invalid token')
    
    def verify_user_exists(self, user_id):
        """Verify user exists in main backend"""
        try:
            # Make request to main backend to verify user
            response = requests.get(
                f"{self.backend_url}/users/{user_id}",
                headers={'Authorization': f'Bearer {self.get_current_token()}'},
                timeout=5
            )
            return response.status_code == 200
        except requests.RequestException:
            # If backend is unreachable, allow the request but log it
            current_app.logger.warning(f"Could not verify user {user_id} - backend unreachable")
            return True
    
    def get_current_token(self):
        """Get current token from request"""
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header.split(' ')[1]
        return None


def require_auth(f):
    """Decorator to require authentication for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_handler = AuthHandler()
        
        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return jsonify({'error': 'Authorization header is required'}), 401
        
        if not auth_header.startswith('Bearer '):
            return jsonify({'error': 'Authorization header must start with Bearer'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # Decode token to get user ID
            user_id = auth_handler.decode_token(token)
            
            # Verify user exists (optional - can be disabled for performance)
            # Uncomment the following lines to enable user verification
            # if not user_service.verify_user_exists(user_id, token):
            #     return jsonify({'error': 'User not found'}), 401
            
            # Add user_id to request context
            request.current_user_id = user_id
            
            return f(*args, **kwargs)
            
        except Exception as e:
            return jsonify({'error': str(e)}), 401
    
    return decorated_function


def require_admin(f):
    """Decorator to require admin authentication for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_handler = AuthHandler()
        
        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return jsonify({'error': 'Authorization header is required'}), 401
        
        if not auth_header.startswith('Bearer '):
            return jsonify({'error': 'Authorization header must start with Bearer'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # Decode token to get user ID
            user_id = auth_handler.decode_token(token)
            
            # Verify user is admin by calling main backend
            try:
                response = requests.get(
                    f"{auth_handler.backend_url}/users/{user_id}",
                    headers={'Authorization': f'Bearer {token}'},
                    timeout=5
                )
                
                if response.status_code != 200:
                    return jsonify({'error': 'User not found'}), 401
                
                user_data = response.json()
                if user_data.get('role') != 'admin':
                    return jsonify({'error': 'Admin access required'}), 403
                    
            except requests.RequestException:
                return jsonify({'error': 'Could not verify admin status'}), 503
            
            # Add user_id to request context
            request.current_user_id = user_id
            
            return f(*args, **kwargs)
            
        except Exception as e:
            return jsonify({'error': str(e)}), 401
    
    return decorated_function


def optional_auth(f):
    """Decorator for optional authentication (adds user_id if token is present)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_handler = AuthHandler()
        
        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            
            try:
                # Decode token to get user ID
                user_id = auth_handler.decode_token(token)
                request.current_user_id = user_id
            except Exception:
                # If token is invalid, continue without authentication
                request.current_user_id = None
        else:
            request.current_user_id = None
        
        return f(*args, **kwargs)
    
    return decorated_function
