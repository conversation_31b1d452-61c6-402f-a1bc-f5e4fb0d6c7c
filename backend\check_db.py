#!/usr/bin/env python3
"""
Simple script to check database contents
"""

import sqlite3
import os

def check_database():
    """Check the database using raw SQL queries"""
    db_path = "DataBase/sql_app.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== DATABASE CONTENTS ===\n")
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 TABLES ({len(tables)} total):")
        for table in tables:
            print(f"  - {table[0]}")
        print()
        
        # Check categories
        try:
            cursor.execute("SELECT * FROM categories;")
            categories = cursor.fetchall()
            print(f"📁 CATEGORIES ({len(categories)} total):")
            for cat in categories:
                print(f"  - ID: {cat[0]}, Name: {cat[1]}")
            print()
        except sqlite3.OperationalError as e:
            print(f"❌ Error reading categories: {e}\n")
        
        # Check ingredients count
        try:
            cursor.execute("SELECT COUNT(*) FROM ingredients;")
            ingredient_count = cursor.fetchone()[0]
            print(f"🥬 INGREDIENTS: {ingredient_count} total")
        except sqlite3.OperationalError as e:
            print(f"❌ Error reading ingredients: {e}")
        
        # Check doners
        try:
            cursor.execute("SELECT COUNT(*) FROM doners;")
            doner_count = cursor.fetchone()[0]
            print(f"🥙 DONERS: {doner_count} total")
            
            if doner_count > 0:
                cursor.execute("SELECT id, name, price FROM doners;")
                doners = cursor.fetchall()
                for doner in doners:
                    print(f"  - ID: {doner[0]}, Name: {doner[1]}, Price: ${doner[2]}")
        except sqlite3.OperationalError as e:
            print(f"❌ Error reading doners: {e}")
        
        # Check kebabs
        try:
            cursor.execute("SELECT COUNT(*) FROM kebabs;")
            kebab_count = cursor.fetchone()[0]
            print(f"🍖 KEBABS: {kebab_count} total")
            
            if kebab_count > 0:
                cursor.execute("SELECT id, name, price FROM kebabs;")
                kebabs = cursor.fetchall()
                for kebab in kebabs:
                    print(f"  - ID: {kebab[0]}, Name: {kebab[1]}, Price: ${kebab[2]}")
        except sqlite3.OperationalError as e:
            print(f"❌ Error reading kebabs: {e}")
        
        # Check salads
        try:
            cursor.execute("SELECT COUNT(*) FROM salads;")
            salad_count = cursor.fetchone()[0]
            print(f"🥗 SALADS: {salad_count} total")
            
            if salad_count > 0:
                cursor.execute("SELECT id, name, price FROM salads;")
                salads = cursor.fetchall()
                for salad in salads:
                    print(f"  - ID: {salad[0]}, Name: {salad[1]}, Price: ${salad[2]}")
        except sqlite3.OperationalError as e:
            print(f"❌ Error reading salads: {e}")
        
        # Check drinks
        try:
            cursor.execute("SELECT COUNT(*) FROM drinks;")
            drink_count = cursor.fetchone()[0]
            print(f"🥤 DRINKS: {drink_count} total")
            
            if drink_count > 0:
                cursor.execute("SELECT id, name, price FROM drinks;")
                drinks = cursor.fetchall()
                for drink in drinks:
                    print(f"  - ID: {drink[0]}, Name: {drink[1]}, Price: ${drink[2]}")
        except sqlite3.OperationalError as e:
            print(f"❌ Error reading drinks: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")

if __name__ == "__main__":
    check_database()
