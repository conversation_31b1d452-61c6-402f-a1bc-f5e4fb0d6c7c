

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from "flowbite-react";
import { productAPI } from "../../../services/api";
import { FaArrowLeft } from "react-icons/fa";
import type { Kebab as KebabType } from "../../../services/api";

const Kebab = () => {
  const { id } = useParams<{ id: string }>();
  const [kebab, setKebab] = useState<KebabType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchKebab = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await productAPI.getKebab(id);
        setKebab(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch kebab');
      } finally {
        setLoading(false);
      }
    };

    fetchKebab();
  }, [id]);

  const getSpiceLevelColor = (level: number) => {
    if (level <= 2) return "success";
    if (level <= 3) return "warning";
    return "failure";
  };

  const getSpiceLevelText = (level: number) => {
    if (level <= 2) return "Mild";
    if (level <= 3) return "Medium";
    return "Spicy";
  };

  const renderSpiceLevel = (level: number) => {
    return (
      <div className="flex items-center space-x-2">
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <span
              key={star}
              className={`text-lg ${
                star <= level ? 'text-red-500' : 'text-gray-300'
              }`}
            >
              🌶️
            </span>
          ))}
        </div>
        <Badge color={getSpiceLevelColor(level)} size="sm">
          {getSpiceLevelText(level)}
        </Badge>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error || !kebab) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error || 'Kebab not found'}</p>
          <Link to="/kebabs">
            <Button className="mt-4">Back to Kebabs</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <Link to="/kebabs" className="inline-flex items-center mb-6 text-blue-600 hover:text-blue-800">
        <FaArrowLeft size={20} color="#3b82f6" />
        Back to Kebabs
      </Link>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Image Section */}
        <div className="space-y-4">
          <img
            src={kebab.image_url || "/api/placeholder/600/400"}
            alt={kebab.name}
            className="w-full h-96 object-cover rounded-lg shadow-lg"
          />
        </div>

        {/* Details Section */}
        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-3xl font-bold text-gray-900">{kebab.name}</h1>
              <Badge color={kebab.is_available ? "success" : "failure"} size="lg">
                {kebab.is_available ? "Available" : "Unavailable"}
              </Badge>
            </div>

            <p className="text-xl font-bold text-green-600 mb-4">
              ${kebab.price.toFixed(2)}
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Description</h3>
              <p className="text-gray-600 leading-relaxed">{kebab.description}</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800 mb-2">Meat Type</h4>
                  <p className="text-gray-600">{kebab.meat_type}</p>
                </div>
              </Card>

              <Card>
                <div className="text-center">
                  <h4 className="font-semibold text-gray-800 mb-2">Spice Level</h4>
                  <div className="flex justify-center">
                    {renderSpiceLevel(kebab.spice_level)}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          <div className="space-y-4">
            <Button
              size="lg"
              className="w-full"
              disabled={!kebab.is_available}
            >
              {kebab.is_available ? 'Add to Cart' : 'Currently Unavailable'}
            </Button>

            <Button
              color="gray"
              size="lg"
              className="w-full"
              disabled={!kebab.is_available}
            >
              Add to Menu
            </Button>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-12">
        <Card>
          <h3 className="text-xl font-bold text-gray-800 mb-4">About This Kebab</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-semibold">Category:</span> Kebab
            </div>
            <div>
              <span className="font-semibold">Meat:</span> {kebab.meat_type}
            </div>
            <div>
              <span className="font-semibold">Spice Level:</span> {getSpiceLevelText(kebab.spice_level)} ({kebab.spice_level}/5)
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Kebab;