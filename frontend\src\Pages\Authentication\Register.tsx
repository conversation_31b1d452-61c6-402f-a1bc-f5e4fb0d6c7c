

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Label, TextInput, Alert, Spinner } from 'flowbite-react';
import { Link, useNavigate } from 'react-router-dom';
import { useUserStore } from '../../store/store';
import { authAPI } from '../../services/api';
import type { RegisterRequest } from '../../services/api';

const Register = () => {
  const [formData, setFormData] = useState<RegisterRequest>({
    username: '',
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    phone_number: '',
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const navigate = useNavigate();
  const { login } = useUserStore();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'confirmPassword') {
      setConfirmPassword(value);
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    // Clear error when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const validateForm = () => {
    if (formData.password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    if (!formData.email.includes('@')) {
      setError('Please enter a valid email address');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Register user
      const newUser = await authAPI.register(formData);

      setSuccess('Account created successfully! Logging you in...');

      // Auto-login after successful registration
      setTimeout(async () => {
        try {
          const loginResponse = await authAPI.login({
            username: formData.username,
            password: formData.password,
          });

          localStorage.setItem('access_token', loginResponse.access_token);

          // Update store with user data
          login({
            id: newUser.id.toString(),
            name: `${newUser.first_name} ${newUser.last_name}`,
            email: newUser.email,
            role: newUser.role,
          });

          // Navigate to login page
          navigate('/login');
        } catch (loginError) {
          // If auto-login fails, redirect to login page
          console.error('Failed to auto-login after registration:', loginError);
          navigate('/login');
        }
      }, 1500);

    } catch (err: unknown) {
      let errorMessage = 'Registration failed. Please try again.';
      if (err && typeof err === 'object' && 'response' in err) {
        const errorObj = err as { response?: { data?: { detail?: string } } };
        errorMessage = errorObj.response?.data?.detail || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center py-12 px-4">
      {/* Background Animation Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 animate-bounce delay-1000">
          <div className="text-6xl opacity-10">🥙</div>
        </div>
        <div className="absolute top-40 right-20 animate-bounce delay-2000">
          <div className="text-4xl opacity-10">🍢</div>
        </div>
        <div className="absolute bottom-20 left-1/4 animate-bounce delay-3000">
          <div className="text-5xl opacity-10">🥗</div>
        </div>
        <div className="absolute bottom-40 right-1/3 animate-bounce delay-4000">
          <div className="text-4xl opacity-10">🧃</div>
        </div>
        <div className="absolute top-1/2 left-10 animate-pulse delay-5000">
          <div className="text-3xl opacity-10">⭐</div>
        </div>
        <div className="absolute top-1/3 right-10 animate-pulse delay-6000">
          <div className="text-3xl opacity-10">🍽️</div>
        </div>
      </div>

      <div className="w-full max-w-2xl relative z-10">
        <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          {/* Logo/Brand Section */}
          <div className="text-center mb-8">
            <div className="text-6xl mb-4 animate-pulse">🍽️</div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-green-500 to-blue-600 bg-clip-text text-transparent mb-2">
              Join Taste of Turkey
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Create your account and start your culinary journey
            </p>
          </div>

          {/* Register Card */}
          <Card className="shadow-2xl border-0 backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 hover:shadow-3xl transition-all duration-300">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                  Create Account
                </h2>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Fill in your details to get started
                </p>
              </div>

              {/* Error Alert */}
              {error && (
                <Alert color="failure" className="animate-shake">
                  <span className="font-medium">Error!</span> {error}
                </Alert>
              )}

              {/* Success Alert */}
              {success && (
                <Alert color="success" className="animate-pulse">
                  <span className="font-medium">Success!</span> {success}
                </Alert>
              )}

              {/* Name Fields Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="first_name" className="text-gray-700 dark:text-gray-300">First Name</Label>
                  <TextInput
                    id="first_name"
                    name="first_name"
                    type="text"
                    placeholder="Enter your first name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="transform transition-all duration-300 focus:scale-105"
                    icon={() => <span className="text-green-500">👤</span>}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="last_name" className="text-gray-700 dark:text-gray-300">Last Name</Label>
                  <TextInput
                    id="last_name"
                    name="last_name"
                    type="text"
                    placeholder="Enter your last name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="transform transition-all duration-300 focus:scale-105"
                    icon={() => <span className="text-green-500">👤</span>}
                  />
                </div>
              </div>

              {/* Username and Email Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-gray-700 dark:text-gray-300">Username</Label>
                  <TextInput
                    id="username"
                    name="username"
                    type="text"
                    placeholder="Choose a username"
                    value={formData.username}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="transform transition-all duration-300 focus:scale-105"
                    icon={() => <span className="text-blue-500">@</span>}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-700 dark:text-gray-300">Email</Label>
                  <TextInput
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Enter your email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="transform transition-all duration-300 focus:scale-105"
                    icon={() => <span className="text-blue-500">📧</span>}
                  />
                </div>
              </div>

              {/* Phone Number */}
              <div className="space-y-2">
                <Label htmlFor="phone_number" className="text-gray-700 dark:text-gray-300">Phone Number (Optional)</Label>
                <TextInput
                  id="phone_number"
                  name="phone_number"
                  type="tel"
                  placeholder="Enter your phone number"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="transform transition-all duration-300 focus:scale-105"
                  icon={() => <span className="text-purple-500">📱</span>}
                />
              </div>

              {/* Password Fields Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-gray-700 dark:text-gray-300">Password</Label>
                  <TextInput
                    id="password"
                    name="password"
                    type="password"
                    placeholder="Create a password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="transform transition-all duration-300 focus:scale-105"
                    icon={() => <span className="text-red-500">🔒</span>}
                  />
                  <p className="text-xs text-gray-500">Minimum 6 characters</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-gray-700 dark:text-gray-300">Confirm Password</Label>
                  <TextInput
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    placeholder="Confirm your password"
                    value={confirmPassword}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="transform transition-all duration-300 focus:scale-105"
                    icon={() => <span className="text-red-500">🔒</span>}
                  />
                </div>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading || !formData.username || !formData.email || !formData.password || !confirmPassword || !formData.first_name || !formData.last_name}
                className="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
                size="lg"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Spinner size="sm" />
                    <span>Creating Account...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span>🎉</span>
                    <span>Create Account</span>
                  </div>
                )}
              </Button>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white dark:bg-gray-800 text-gray-500">
                    Already have an account?
                  </span>
                </div>
              </div>

              {/* Login Link */}
              <div className="text-center">
                <Link
                  to="/login"
                  className="text-green-500 hover:text-green-600 font-medium hover:underline transition-all duration-300 transform hover:scale-105 inline-block"
                >
                  Sign in to your account →
                </Link>
              </div>
            </form>
          </Card>

          {/* Benefits */}
          <div className="mt-8 text-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              Why join Taste of Turkey?
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex flex-col items-center gap-1">
                <span className="text-2xl">🚚</span>
                <span>Fast Delivery</span>
              </div>
              <div className="flex flex-col items-center gap-1">
                <span className="text-2xl">📅</span>
                <span>Easy Reservations</span>
              </div>
              <div className="flex flex-col items-center gap-1">
                <span className="text-2xl">⭐</span>
                <span>Loyalty Rewards</span>
              </div>
              <div className="flex flex-col items-center gap-1">
                <span className="text-2xl">🎁</span>
                <span>Special Offers</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
