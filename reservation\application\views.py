from rest_framework import generics, status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.utils import timezone
from datetime import datetime, time
from .models import Table, Reservation
from .serializers import (
    TableSerializer,
    ReservationSerializer,
    ReservationCreateSerializer,
    AvailableTablesSerializer
)
from .auth import require_auth, require_admin, optional_auth

class TableListView(generics.ListAPIView):
    """List all tables - no authentication required for viewing tables"""
    queryset = Table.objects.all()
    serializer_class = TableSerializer

class TableDetailView(generics.RetrieveAPIView):
    """Get details of a specific table - no authentication required"""
    queryset = Table.objects.all()
    serializer_class = TableSerializer

class ReservationListCreateView(generics.ListCreateAPIView):
    """List all reservations and create new reservations"""
    queryset = Reservation.objects.all()

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ReservationCreateSerializer
        return ReservationSerializer

    def dispatch(self, request, *args, **kwargs):
        """Add authentication check"""
        from .auth import AuthHandler

        # For POST requests (creating reservations), require authentication
        if request.method == 'POST':
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if not auth_header:
                return Response({'error': 'Authorization header is required'}, status=401)

            if not auth_header.startswith('Bearer '):
                return Response({'error': 'Authorization header must start with Bearer'}, status=401)

            token = auth_header.split(' ')[1]
            auth_handler = AuthHandler()

            try:
                user_id = auth_handler.decode_token(token)
                request.current_user_id = user_id
                request.current_token = token
            except Exception as e:
                return Response({'error': str(e)}, status=401)

        # For GET requests, check if user is admin to see all reservations
        elif request.method == 'GET':
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                auth_handler = AuthHandler()

                try:
                    user_id = auth_handler.decode_token(token)
                    request.current_user_id = user_id
                    request.current_token = token
                except Exception:
                    request.current_user_id = None
                    request.current_token = None
            else:
                request.current_user_id = None
                request.current_token = None

        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        """Filter reservations based on query parameters and user permissions"""
        queryset = Reservation.objects.all()

        # If user is authenticated, check if they should see all reservations or just their own
        current_user_id = getattr(self.request, 'current_user_id', None)
        if current_user_id:
            # Check if user is admin (simplified - in production you'd verify this properly)
            # For now, non-admin users can only see their own reservations
            # You can add admin check here by calling the main backend
            queryset = queryset.filter(user_id=current_user_id)

        # Filter by date
        date = self.request.query_params.get('date')
        if date:
            try:
                filter_date = datetime.strptime(date, '%Y-%m-%d').date()
                queryset = queryset.filter(reservation_date=filter_date)
            except ValueError:
                pass

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by customer email
        email = self.request.query_params.get('email')
        if email:
            queryset = queryset.filter(customer_email__icontains=email)

        return queryset

    def perform_create(self, serializer):
        """Associate reservation with authenticated user"""
        current_user_id = getattr(self.request, 'current_user_id', None)
        serializer.save(user_id=current_user_id)

class ReservationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Get, update, or delete a specific reservation"""
    queryset = Reservation.objects.all()
    serializer_class = ReservationSerializer

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ReservationCreateSerializer
        return ReservationSerializer

    def dispatch(self, request, *args, **kwargs):
        """Add authentication check"""
        from .auth import AuthHandler

        # Require authentication for all operations on specific reservations
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            return Response({'error': 'Authorization header is required'}, status=401)

        if not auth_header.startswith('Bearer '):
            return Response({'error': 'Authorization header must start with Bearer'}, status=401)

        token = auth_header.split(' ')[1]
        auth_handler = AuthHandler()

        try:
            user_id = auth_handler.decode_token(token)
            request.current_user_id = user_id
            request.current_token = token
        except Exception as e:
            return Response({'error': str(e)}, status=401)

        return super().dispatch(request, *args, **kwargs)

    def get_object(self):
        """Get reservation and check ownership"""
        obj = super().get_object()
        current_user_id = getattr(self.request, 'current_user_id', None)

        # Users can only access their own reservations
        if obj.user_id != current_user_id:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("Access denied")

        return obj

@api_view(['POST'])
@optional_auth
def check_availability(request):
    """Check available tables for a specific date, time, and party size"""
    serializer = AvailableTablesSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    data = serializer.validated_data
    reservation_date = data['reservation_date']
    reservation_time = data['reservation_time']
    party_size = data['party_size']
    duration_hours = data.get('duration_hours', 2)

    # Calculate end time
    start_datetime = datetime.combine(reservation_date, reservation_time)
    end_datetime = start_datetime.replace(hour=start_datetime.hour + duration_hours)

    # Get all tables that can accommodate the party size
    suitable_tables = Table.objects.filter(
        capacity__gte=party_size,
        is_available=True
    )

    available_tables = []

    for table in suitable_tables:
        # Check for overlapping reservations
        overlapping_reservations = Reservation.objects.filter(
            table=table,
            reservation_date=reservation_date,
            status__in=['pending', 'confirmed']
        )

        is_available = True
        for reservation in overlapping_reservations:
            existing_start = datetime.combine(reservation.reservation_date, reservation.reservation_time)
            existing_end = existing_start.replace(hour=existing_start.hour + reservation.duration_hours)

            # Check for time overlap
            if (start_datetime < existing_end and end_datetime > existing_start):
                is_available = False
                break

        if is_available:
            available_tables.append(table)

    # Serialize available tables
    table_serializer = TableSerializer(available_tables, many=True)

    return Response({
        'available_tables': table_serializer.data,
        'requested_date': reservation_date,
        'requested_time': reservation_time,
        'party_size': party_size,
        'duration_hours': duration_hours
    })

@api_view(['POST'])
@require_auth
def cancel_reservation(request, pk):
    """Cancel a reservation"""
    try:
        reservation = Reservation.objects.get(pk=pk)
    except Reservation.DoesNotExist:
        return Response({'error': 'Reservation not found'}, status=status.HTTP_404_NOT_FOUND)

    # Check if user owns this reservation
    current_user_id = getattr(request, 'current_user_id', None)
    if reservation.user_id != current_user_id:
        return Response({'error': 'Access denied'}, status=status.HTTP_403_FORBIDDEN)

    # Check if reservation can be cancelled (not in the past and not already completed)
    if reservation.is_past_due:
        return Response({'error': 'Cannot cancel past reservations'}, status=status.HTTP_400_BAD_REQUEST)

    if reservation.status in ['cancelled', 'completed']:
        return Response({'error': f'Reservation is already {reservation.status}'}, status=status.HTTP_400_BAD_REQUEST)

    reservation.status = 'cancelled'
    reservation.save()

    serializer = ReservationSerializer(reservation)
    return Response(serializer.data)

@api_view(['POST'])
@require_admin
def confirm_reservation(request, pk):
    """Confirm a reservation"""
    try:
        reservation = Reservation.objects.get(pk=pk)
    except Reservation.DoesNotExist:
        return Response({'error': 'Reservation not found'}, status=status.HTTP_404_NOT_FOUND)

    if reservation.status != 'pending':
        return Response({'error': f'Reservation is already {reservation.status}'}, status=status.HTTP_400_BAD_REQUEST)

    reservation.status = 'confirmed'
    reservation.save()

    serializer = ReservationSerializer(reservation)
    return Response(serializer.data)

@api_view(['GET'])
@require_admin
def reservation_stats(request):
    """Get reservation statistics"""
    today = timezone.now().date()

    stats = {
        'total_reservations': Reservation.objects.count(),
        'today_reservations': Reservation.objects.filter(reservation_date=today).count(),
        'pending_reservations': Reservation.objects.filter(status='pending').count(),
        'confirmed_reservations': Reservation.objects.filter(status='confirmed').count(),
        'cancelled_reservations': Reservation.objects.filter(status='cancelled').count(),
        'total_tables': Table.objects.count(),
        'available_tables': Table.objects.filter(is_available=True).count(),
    }

    return Response(stats)
