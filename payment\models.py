from main import db
from datetime import datetime
from enum import Enum

class PaymentStatus(Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"

class PaymentMethod(Enum):
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    BANK_TRANSFER = "bank_transfer"

class Customer(db.Model):
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.String(100), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    surname = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    identity_number = db.Column(db.String(20), nullable=False)
    
    # Address information
    address = db.Column(db.Text, nullable=False)
    city = db.Column(db.String(50), nullable=False)
    country = db.Column(db.String(50), nullable=False)
    zip_code = db.Column(db.String(10), nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    payments = db.relationship('Payment', backref='customer', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'customer_id': self.customer_id,
            'name': self.name,
            'surname': self.surname,
            'email': self.email,
            'phone': self.phone,
            'identity_number': self.identity_number,
            'address': self.address,
            'city': self.city,
            'country': self.country,
            'zip_code': self.zip_code,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Payment(db.Model):
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    payment_id = db.Column(db.String(100), unique=True, nullable=False)
    conversation_id = db.Column(db.String(100), nullable=False)
    
    # Customer reference
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)

    # User reference (from main backend)
    user_id = db.Column(db.Integer, nullable=False)  # Reference to user in main backend

    # Payment details
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    currency = db.Column(db.String(3), default='TRY', nullable=False)
    payment_method = db.Column(db.Enum(PaymentMethod), nullable=False)
    
    # Status and tracking
    status = db.Column(db.Enum(PaymentStatus), default=PaymentStatus.PENDING, nullable=False)
    iyzico_payment_id = db.Column(db.String(100), nullable=True)
    iyzico_payment_status = db.Column(db.String(50), nullable=True)
    
    # Order information
    order_id = db.Column(db.String(100), nullable=False)
    order_description = db.Column(db.Text, nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    paid_at = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    transactions = db.relationship('Transaction', backref='payment', lazy=True)
    basket_items = db.relationship('BasketItem', backref='payment', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'payment_id': self.payment_id,
            'conversation_id': self.conversation_id,
            'customer_id': self.customer_id,
            'user_id': self.user_id,
            'amount': float(self.amount),
            'currency': self.currency,
            'payment_method': self.payment_method.value if self.payment_method else None,
            'status': self.status.value if self.status else None,
            'iyzico_payment_id': self.iyzico_payment_id,
            'iyzico_payment_status': self.iyzico_payment_status,
            'order_id': self.order_id,
            'order_description': self.order_description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'paid_at': self.paid_at.isoformat() if self.paid_at else None
        }

class Transaction(db.Model):
    __tablename__ = 'transactions'
    
    id = db.Column(db.Integer, primary_key=True)
    transaction_id = db.Column(db.String(100), unique=True, nullable=False)
    
    # Payment reference
    payment_id = db.Column(db.Integer, db.ForeignKey('payments.id'), nullable=False)
    
    # Transaction details
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    transaction_type = db.Column(db.String(50), nullable=False)  # payment, refund, etc.
    
    # Iyzico transaction details
    iyzico_transaction_id = db.Column(db.String(100), nullable=True)
    iyzico_status = db.Column(db.String(50), nullable=True)
    
    # Response data
    response_data = db.Column(db.Text, nullable=True)  # Store full Iyzico response
    error_message = db.Column(db.Text, nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'transaction_id': self.transaction_id,
            'payment_id': self.payment_id,
            'amount': float(self.amount),
            'transaction_type': self.transaction_type,
            'iyzico_transaction_id': self.iyzico_transaction_id,
            'iyzico_status': self.iyzico_status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class BasketItem(db.Model):
    __tablename__ = 'basket_items'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Payment reference
    payment_id = db.Column(db.Integer, db.ForeignKey('payments.id'), nullable=False)
    
    # Item details
    item_id = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    category1 = db.Column(db.String(100), nullable=False)
    category2 = db.Column(db.String(100), nullable=True)
    item_type = db.Column(db.String(50), nullable=False)  # PHYSICAL, VIRTUAL
    price = db.Column(db.Numeric(10, 2), nullable=False)
    quantity = db.Column(db.Integer, default=1, nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'payment_id': self.payment_id,
            'item_id': self.item_id,
            'name': self.name,
            'category1': self.category1,
            'category2': self.category2,
            'item_type': self.item_type,
            'price': float(self.price),
            'quantity': self.quantity,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
