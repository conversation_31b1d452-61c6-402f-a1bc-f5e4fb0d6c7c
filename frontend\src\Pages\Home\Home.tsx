import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'flowbite-react';
import { Link } from 'react-router-dom';

const Home = () => {

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Function to generate route path based on category name
  const getCategoryRoute = (categoryName: string) => {
    switch (categoryName.toLowerCase()) {
      case 'doner':
        return '/doners';
      case 'kebab':
        return '/kebabs';
      case 'salad':
        return '/salads';
      case 'drinks':
        return '/drinks';
      default:
        return '/menus';
    }
  };

  const menuCategories = [
    {
      name: 'Doner',
      description: 'Authentic Turkish doner with fresh ingredients',
      image: '🥙',
      color: 'bg-gradient-to-br from-orange-400 to-red-500',
      delay: 'delay-100'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      description: 'Grilled to perfection with traditional spices',
      image: '🍢',
      color: 'bg-gradient-to-br from-green-400 to-blue-500',
      delay: 'delay-200'
    },
    {
      name: 'Salad',
      description: 'Fresh and healthy Mediterranean salads',
      image: '🥗',
      color: 'bg-gradient-to-br from-green-300 to-emerald-500',
      delay: 'delay-300'
    },
    {
      name: 'Drinks',
      description: 'Refreshing beverages and traditional Turkish tea',
      image: '🧃',
      color: 'bg-gradient-to-br from-blue-400 to-purple-500',
      delay: 'delay-400'
    }
  ];

  const features = [
    {
      icon: '🚚',
      title: 'Fast Delivery',
      description: 'Quick delivery to your doorstep'
    },
    {
      icon: '📅',
      title: 'Online Reservations',
      description: 'Book your table in advance'
    },
    {
      icon: '👨‍🍳',
      title: 'Expert Chefs',
      description: 'Authentic Turkish cuisine masters'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4">
        <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-red-500/20 animate-pulse"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 animate-bounce delay-1000">
          <div className="text-6xl opacity-20">🥙</div>
        </div>
        <div className="absolute top-40 right-20 animate-bounce delay-2000">
          <div className="text-4xl opacity-20">🍢</div>
        </div>
        <div className="absolute bottom-20 left-1/4 animate-bounce delay-3000">
          <div className="text-5xl opacity-20">🥗</div>
        </div>

        <div className="container mx-auto text-center relative z-10">
          <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <h1 className="text-6xl md:text-8xl font-bold mb-6 bg-gradient-to-r from-orange-500 to-red-600 bg-clip-text text-transparent animate-pulse">
              Taste of Turkey
            </h1>
            <p className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-8 max-w-3xl mx-auto italic">
              Experience Authentic Turkish Flavors With Our Traditional Doner, Kebabs, Fresh Salads, and Premium Drinks
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="xl"
                className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                <Link to="/makeOrder" className="flex items-center gap-2">
                  🍽️ ORDER NOW
                </Link>
              </Button>
              <Button
                size="xl"
                color="light"
                className="transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                <Link to="/makeReservation" className="flex items-center gap-2">
                  📅 MAKE RESERVATION
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Menu Categories */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-amber-600 dark:text-white">
              OUR COUISINE
            </h2>
            <p className="text-2xl text-gray-600 dark:text-gray-400 italic">
              Discover Our Delicious Cuisine
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {menuCategories.map((category) => (
              <div
                key={category.name}
                className={`transform transition-all duration-1000 hover:scale-105 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'} ${category.delay}`}
              >
                <Card className="h-full hover:shadow-2xl transition-all duration-300 border-0 overflow-hidden group">
                  <div className={`${category.color} p-8 text-center relative overflow-hidden`}>
                    <div className="absolute inset-0 bg-white/10 transform -skew-y-6 group-hover:skew-y-6 transition-transform duration-500"></div>
                    <div className="text-6xl mb-4 animate-bounce relative z-10">{category.image}</div>
                    <h3 className="text-2xl font-bold text-white relative z-10 uppercase">{category.name}</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 dark:text-gray-400 text-center italic">
                      {category.description}
                    </p>
                    <Link to={getCategoryRoute(category.name)}>
                      <Button
                        className="cursor-pointer w-full mt-4 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 uppercase"
                      >
                        VIEW {category.name}
                      </Button>
                    </Link>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white dark:bg-gray-800">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-amber-600 dark:text-white">
              WHY CHOOSE US?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={feature.title}
                className={`text-center transform transition-all duration-1000 hover:scale-105 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
                style={{ animationDelay: `${(index + 1) * 200}ms` }}
              >
                <div className="text-6xl mb-4 animate-bounce">{feature.icon}</div>
                <h3 className="text-2xl font-bold mb-2 text-amber-600 dark:text-white">{feature.title}</h3>
                <p className="text-gray-600 dark:text-gray-400 italic">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 px-4 bg-gradient-to-r from-orange-500 to-red-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Floating Food Icons */}
        <div className="absolute top-10 left-10 animate-spin slow-spin">
          <div className="text-4xl opacity-30">🥙</div>
        </div>
        <div className="absolute bottom-10 right-10 animate-spin slow-spin">
          <div className="text-4xl opacity-30">🍢</div>
        </div>
        <div className="absolute top-1/2 left-1/4 animate-pulse">
          <div className="text-3xl opacity-20">🥗</div>
        </div>

        <div className="container mx-auto text-center relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Experience Turkish Cuisine?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers who have made us their favorite Turkish restaurant
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="xl"
              color="light"
              className="transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <Link to="/makeOrder" className="flex items-center gap-2">
                🛒 START ORDERING
              </Link>
            </Button>
            <Button
              size="xl"
              color="light"
              className="transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <Link to="/about" className="flex items-center gap-2">
                📞 CONTACT US
              </Link>
            </Button>
          </div>
        </div>
      </section>

    </div>
  );
};

export default Home;
