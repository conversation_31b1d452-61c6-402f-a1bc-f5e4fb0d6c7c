
import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, Button, Badge, Spinner } from "flowbite-react";
import { productAPI } from "../../../services/api";
import type { Doner as DonerType } from "../../../services/api";

const Doners = () => {
  const [doners, setDoners] = useState<DonerType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDoners = async () => {
      try {
        setLoading(true);
        const data = await productAPI.getDoners();
        setDoners(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch doners');
      } finally {
        setLoading(false);
      }
    };

    fetchDoners();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Our Delicious Doners</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Discover our authentic Turkish doners, made with the finest ingredients and traditional recipes.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {doners.map((doner) => (
          <Card key={doner.id} className="max-w-sm mx-auto">
            <img
              src={doner.image_url || "/api/placeholder/300/200"}
              alt={doner.name}
              className="h-48 w-full object-cover rounded-t-lg"
            />
            <div className="p-5">
              <div className="flex justify-between items-start mb-2">
                <h5 className="text-xl font-bold tracking-tight text-gray-900">
                  {doner.name}
                </h5>
                <Badge color={doner.is_available ? "success" : "failure"}>
                  {doner.is_available ? "Available" : "Unavailable"}
                </Badge>
              </div>

              <p className="font-normal text-gray-700 mb-3 line-clamp-3">
                {doner.description}
              </p>

              <div className="flex justify-between items-center mb-3">
                <span className="text-sm text-gray-500">Meat: {doner.meat_type}</span>
                <span className="text-sm text-gray-500">Style: {doner.style}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-gray-900">
                  ${doner.price.toFixed(2)}
                </span>
                <Link to={`/doners/${doner.id}`}>
                  <Button size="sm" disabled={!doner.is_available}>
                    View Details
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {doners.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold text-gray-600 mb-2">No doners available</h3>
          <p className="text-gray-500">Check back later for our delicious doners!</p>
        </div>
      )}
    </div>
  );
};

export default Doners;