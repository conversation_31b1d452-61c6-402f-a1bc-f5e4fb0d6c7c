from sqlalchemy import Column, Integer, String, Float, Foreign<PERSON>ey, Boolean, CheckConstraint
from sqlalchemy.orm import relationship
from ...DataBase.DataBase import Base

class Menu(Base):
    __tablename__ = 'menus'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255))
    price = Column(Float, nullable=False)
    image_url = Column(String(255))
    is_available = Column(Boolean, default=True)

    # Foreign Keys to actual food items
    drink_id = Column(Integer, ForeignKey('drinks.id'), nullable=False)
    salad_id = Column(Integer, ForeignKey('salads.id'), nullable=False)

    # Main course can be either kebab or doner (nullable, but one must be set)
    kebab_id = Column(Integer, ForeignKey('kebabs.id'), nullable=True)
    doner_id = Column(Integer, ForeignKey('doners.id'), nullable=True)

    # Relationships to actual food models
    drink = relationship("Drink", foreign_keys=[drink_id])
    salad = relationship("Salad", foreign_keys=[salad_id])
    kebab = relationship("Kebab", foreign_keys=[kebab_id])
    doner = relationship("Doner", foreign_keys=[doner_id])

    # Temporarily disabled due to MenuItem dependency
    # order_items = relationship("OrderItem", back_populates="menu")

    # Ensure that exactly one main course is selected (either kebab or doner, but not both)
    __table_args__ = (
        CheckConstraint(
            '(kebab_id IS NOT NULL AND doner_id IS NULL) OR (kebab_id IS NULL AND doner_id IS NOT NULL)',
            name='check_menu_main_course'
        ),
    )

    def __repr__(self):
        main_course = f"Kebab({self.kebab_id})" if self.kebab_id else f"Doner({self.doner_id})"
        return f"<Menu(id={self.id}, name='{self.name}', main_course={main_course})>"

    @property
    def main_course(self):
        """Get the main course item (either kebab or doner)"""
        return self.kebab if self.kebab_id else self.doner

    @property
    def main_course_type(self):
        """Get the type of main course"""
        return "kebab" if self.kebab_id else "doner"
