import requests
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class UserService:
    """Utility class to fetch user data from main backend"""
    
    def __init__(self, backend_url: str = 'http://localhost:8000'):
        self.backend_url = backend_url
    
    def get_user_data(self, user_id: int, token: str) -> Optional[Dict[Any, Any]]:
        """
        Fetch user data from main backend
        
        Args:
            user_id: The user ID to fetch
            token: JWT token for authentication
            
        Returns:
            User data dictionary or None if not found
        """
        try:
            response = requests.get(
                f"{self.backend_url}/users/{user_id}",
                headers={'Authorization': f'Bearer {token}'},
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed to fetch user {user_id}: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"Error fetching user {user_id}: {str(e)}")
            return None
    
    def get_user_profile(self, token: str) -> Optional[Dict[Any, Any]]:
        """
        Fetch current user profile from main backend
        
        Args:
            token: JWT token for authentication
            
        Returns:
            User profile dictionary or None if not found
        """
        try:
            response = requests.get(
                f"{self.backend_url}/users/me",
                headers={'Authorization': f'Bearer {token}'},
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed to fetch user profile: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"Error fetching user profile: {str(e)}")
            return None
    
    def verify_user_exists(self, user_id: int, token: str) -> bool:
        """
        Verify if a user exists in the main backend
        
        Args:
            user_id: The user ID to verify
            token: JWT token for authentication
            
        Returns:
            True if user exists, False otherwise
        """
        user_data = self.get_user_data(user_id, token)
        return user_data is not None

# Global instance
user_service = UserService()
