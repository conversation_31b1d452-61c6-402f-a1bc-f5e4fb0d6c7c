from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List

from ...Controllers.Message.MessageControllers import MessageControllers
from ...DataBase.DataBase import get_db
from ...Schemas.MessageSchemas.MessageSchemas import Message, MessageCreate, MessageUpdate
from ...Utils.JWT import AuthHandler

Message_Router = APIRouter(
    prefix="/messages",
    tags=["messages"],
)

auth_handler = AuthHandler()

# Create Message (Send message to admin)
@Message_Router.post("/", response_model=Message)
async def create_message_route(
    message: MessageCreate,
    current_user_id: int = Depends(auth_handler.auth_wrapper),
    db: Session = Depends(get_db)
):
    return await MessageControllers.create_message(db, message, current_user_id)

# Get All Messages
@Message_Router.get("/", response_model=List[Message])
async def read_messages_route(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    return await MessageControllers.get_messages(db, skip, limit)

# Get Single Message
@Message_Router.get("/{message_id}", response_model=Message)
async def read_message_route(message_id: int, db: Session = Depends(get_db)):
    return await MessageControllers.get_message(db, message_id)

# Update Message
@Message_Router.put("/{message_id}", response_model=Message)
async def update_message_route(message_id: int, message: MessageUpdate, db: Session = Depends(get_db)):
    return await MessageControllers.update_message(db, message_id, message)

# Delete Message
@Message_Router.delete("/{message_id}", response_model=Message)
async def delete_message_route(message_id: int, db: Session = Depends(get_db)):
    return await MessageControllers.delete_message(db, message_id)