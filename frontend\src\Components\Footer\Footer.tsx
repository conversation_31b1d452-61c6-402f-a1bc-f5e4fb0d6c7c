  import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON><PERSON>, FooterDivider, FooterLink, FooterLinkGroup, Tooltip } from "flowbite-react";
  import Logo from '../../assets/logo.png'
  import { FaGithub } from "react-icons/fa";
  import { MdEmail } from "react-icons/md";



  const FooterComponent = () => {

    const CurrentYear : number = 2025

    return (
      <Footer container>
        <div className="w-full text-center">
          <div className="w-full justify-between sm:flex sm:items-center sm:justify-between">
            <FooterBrand
              href="/"
              src={Logo}
              alt="AUP Logo"
            />
            <FooterLinkGroup>
              <FooterLink href="https://github.com/AhmetUtkuPelen" target="_blank">
                <FaGithub size={28} color="black"/>
              </FooterLink>
            
              <Tooltip content="<EMAIL>" placement="right">
                <FooterLink href="mailto:<EMAIL>">
                  <MdEmail size={28} color="black"/>
                </FooterLink>
              </Tooltip>
            
            </FooterLinkGroup>
          </div>
          <FooterDivider />
          <FooterCopyright href="/" by="AHMET UTKU PELEN ™" year={CurrentYear} className="font-bold italic" />
        </div>
      </Footer>
    );
  }

  export default FooterComponent;