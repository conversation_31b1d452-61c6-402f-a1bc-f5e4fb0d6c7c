from rest_framework import serializers
from .models import Table, Reservation
from django.utils import timezone
from datetime import datetime, time

class TableSerializer(serializers.ModelSerializer):
    """Serializer for Table model"""
    
    class Meta:
        model = Table
        fields = ['id', 'table_number', 'capacity', 'is_available', 'location', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class ReservationSerializer(serializers.ModelSerializer):
    """Serializer for Reservation model"""
    table_details = TableSerializer(source='table', read_only=True)
    
    class Meta:
        model = Reservation
        fields = [
            'id', 'customer_name', 'customer_email', 'customer_phone',
            'table', 'table_details', 'reservation_date', 'reservation_time',
            'party_size', 'duration_hours', 'status', 'special_requests',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_reservation_date(self, value):
        """Validate that reservation date is not in the past"""
        if value < timezone.now().date():
            raise serializers.ValidationError("Reservation date cannot be in the past")
        return value
    
    def validate_reservation_time(self, value):
        """Validate reservation time"""
        # Check if time is within restaurant hours (9 AM to 11 PM)
        opening_time = time(9, 0)  # 9:00 AM
        closing_time = time(23, 0)  # 11:00 PM
        
        if value < opening_time or value > closing_time:
            raise serializers.ValidationError("Reservations are only available between 9:00 AM and 11:00 PM")
        return value
    
    def validate(self, data):
        """Cross-field validation"""
        # Check if reservation datetime is not in the past
        if 'reservation_date' in data and 'reservation_time' in data:
            reservation_datetime = datetime.combine(data['reservation_date'], data['reservation_time'])
            if timezone.make_aware(reservation_datetime) < timezone.now():
                raise serializers.ValidationError("Cannot make reservations in the past")
        
        # Check if party size doesn't exceed table capacity
        if 'table' in data and 'party_size' in data:
            if data['party_size'] > data['table'].capacity:
                raise serializers.ValidationError(
                    f"Party size ({data['party_size']}) exceeds table capacity ({data['table'].capacity})"
                )
        
        return data

class ReservationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating reservations with table availability check"""
    
    class Meta:
        model = Reservation
        fields = [
            'customer_name', 'customer_email', 'customer_phone',
            'table', 'reservation_date', 'reservation_time',
            'party_size', 'duration_hours', 'special_requests'
        ]
    
    def validate_reservation_date(self, value):
        """Validate that reservation date is not in the past"""
        if value < timezone.now().date():
            raise serializers.ValidationError("Reservation date cannot be in the past")
        return value
    
    def validate_reservation_time(self, value):
        """Validate reservation time"""
        # Check if time is within restaurant hours (9 AM to 11 PM)
        opening_time = time(9, 0)  # 9:00 AM
        closing_time = time(23, 0)  # 11:00 PM
        
        if value < opening_time or value > closing_time:
            raise serializers.ValidationError("Reservations are only available between 9:00 AM and 11:00 PM")
        return value
    
    def validate(self, data):
        """Cross-field validation including table availability"""
        # Check if reservation datetime is not in the past
        if 'reservation_date' in data and 'reservation_time' in data:
            reservation_datetime = datetime.combine(data['reservation_date'], data['reservation_time'])
            if timezone.make_aware(reservation_datetime) < timezone.now():
                raise serializers.ValidationError("Cannot make reservations in the past")
        
        # Check if party size doesn't exceed table capacity
        if 'table' in data and 'party_size' in data:
            if data['party_size'] > data['table'].capacity:
                raise serializers.ValidationError(
                    f"Party size ({data['party_size']}) exceeds table capacity ({data['table'].capacity})"
                )
        
        # Check table availability for the requested time slot
        if all(key in data for key in ['table', 'reservation_date', 'reservation_time', 'duration_hours']):
            table = data['table']
            reservation_date = data['reservation_date']
            reservation_time = data['reservation_time']
            duration = data['duration_hours']
            
            # Calculate end time
            start_datetime = datetime.combine(reservation_date, reservation_time)
            end_datetime = start_datetime.replace(hour=start_datetime.hour + duration)
            
            # Check for overlapping reservations
            overlapping_reservations = Reservation.objects.filter(
                table=table,
                reservation_date=reservation_date,
                status__in=['pending', 'confirmed']
            ).exclude(id=self.instance.id if self.instance else None)
            
            for reservation in overlapping_reservations:
                existing_start = datetime.combine(reservation.reservation_date, reservation.reservation_time)
                existing_end = existing_start.replace(hour=existing_start.hour + reservation.duration_hours)
                
                # Check for time overlap
                if (start_datetime < existing_end and end_datetime > existing_start):
                    raise serializers.ValidationError(
                        f"Table {table.table_number} is already reserved during this time slot"
                    )
        
        return data

class AvailableTablesSerializer(serializers.Serializer):
    """Serializer for checking available tables"""
    reservation_date = serializers.DateField()
    reservation_time = serializers.TimeField()
    party_size = serializers.IntegerField(min_value=1, max_value=20)
    duration_hours = serializers.IntegerField(min_value=1, max_value=8, default=2)
    
    def validate_reservation_date(self, value):
        """Validate that reservation date is not in the past"""
        if value < timezone.now().date():
            raise serializers.ValidationError("Reservation date cannot be in the past")
        return value
    
    def validate_reservation_time(self, value):
        """Validate reservation time"""
        # Check if time is within restaurant hours (9 AM to 11 PM)
        opening_time = time(9, 0)  # 9:00 AM
        closing_time = time(23, 0)  # 11:00 PM
        
        if value < opening_time or value > closing_time:
            raise serializers.ValidationError("Reservations are only available between 9:00 AM and 11:00 PM")
        return value
